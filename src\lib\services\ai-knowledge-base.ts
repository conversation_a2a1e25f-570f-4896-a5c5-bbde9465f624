/**
 * AI识别知识库服务
 * 用于存储和管理成功识别的案例，提升识别速度和准确性
 */

export interface RecognitionCase {
  id: string
  originalText: string
  recognizedData: any
  confidence: number
  createdAt: string
  verifiedBy?: string
  tags: string[]
  patterns: {
    projectPattern?: string
    floorPattern?: string
    ventPatterns: string[]
    dimensionPatterns: string[]
  }
}

export interface KnowledgeBaseStats {
  totalCases: number
  averageConfidence: number
  commonPatterns: {
    projects: string[]
    ventTypes: string[]
    dimensionFormats: string[]
  }
}

class AIKnowledgeBaseService {
  private readonly STORAGE_KEY = 'ai_recognition_knowledge_base'
  private readonly MAX_CASES = 1000 // 最大存储案例数

  /**
   * 保存成功识别的案例
   */
  async saveRecognitionCase(
    originalText: string,
    recognizedData: any,
    confidence: number,
    verifiedBy?: string
  ): Promise<string> {
    try {
      const cases = this.loadCases()
      
      // 检查是否已存在相似案例
      const similarCase = this.findSimilarCase(originalText, cases)
      if (similarCase) {
        console.log('🔍 发现相似案例，更新置信度')
        return this.updateCase(similarCase.id, recognizedData, confidence)
      }

      // 创建新案例
      const newCase: RecognitionCase = {
        id: this.generateId(),
        originalText: originalText.trim(),
        recognizedData,
        confidence,
        createdAt: new Date().toISOString(),
        verifiedBy,
        tags: this.extractTags(originalText, recognizedData),
        patterns: this.extractPatterns(originalText, recognizedData)
      }

      cases.push(newCase)

      // 保持案例数量在限制内
      if (cases.length > this.MAX_CASES) {
        cases.sort((a, b) => b.confidence - a.confidence)
        cases.splice(this.MAX_CASES)
      }

      this.saveCases(cases)
      console.log(`✅ 保存识别案例: ${newCase.id}`)
      return newCase.id
    } catch (error) {
      console.error('❌ 保存识别案例失败:', error)
      throw error
    }
  }

  /**
   * 查找相似案例以加速识别
   */
  findSimilarCases(inputText: string, limit: number = 5): RecognitionCase[] {
    try {
      const cases = this.loadCases()
      const similarities = cases.map(case_ => ({
        case: case_,
        similarity: this.calculateSimilarity(inputText, case_.originalText)
      }))

      return similarities
        .filter(item => item.similarity > 0.7) // 相似度阈值
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, limit)
        .map(item => item.case)
    } catch (error) {
      console.error('❌ 查找相似案例失败:', error)
      return []
    }
  }

  /**
   * 获取知识库统计信息
   */
  getKnowledgeBaseStats(): KnowledgeBaseStats {
    try {
      const cases = this.loadCases()
      
      if (cases.length === 0) {
        return {
          totalCases: 0,
          averageConfidence: 0,
          commonPatterns: {
            projects: [],
            ventTypes: [],
            dimensionFormats: []
          }
        }
      }

      const totalConfidence = cases.reduce((sum, case_) => sum + case_.confidence, 0)
      const averageConfidence = totalConfidence / cases.length

      // 统计常见模式
      const projectPatterns = new Set<string>()
      const ventTypes = new Set<string>()
      const dimensionFormats = new Set<string>()

      cases.forEach(case_ => {
        if (case_.patterns.projectPattern) {
          projectPatterns.add(case_.patterns.projectPattern)
        }
        case_.patterns.ventPatterns.forEach(pattern => ventTypes.add(pattern))
        case_.patterns.dimensionPatterns.forEach(pattern => dimensionFormats.add(pattern))
      })

      return {
        totalCases: cases.length,
        averageConfidence: Math.round(averageConfidence * 100) / 100,
        commonPatterns: {
          projects: Array.from(projectPatterns).slice(0, 10),
          ventTypes: Array.from(ventTypes).slice(0, 10),
          dimensionFormats: Array.from(dimensionFormats).slice(0, 10)
        }
      }
    } catch (error) {
      console.error('❌ 获取知识库统计失败:', error)
      return {
        totalCases: 0,
        averageConfidence: 0,
        commonPatterns: { projects: [], ventTypes: [], dimensionFormats: [] }
      }
    }
  }

  /**
   * 生成智能提示
   */
  generateSmartPrompt(inputText: string, basePrompt: string): string {
    try {
      const similarCases = this.findSimilarCases(inputText, 3)
      
      if (similarCases.length === 0) {
        return basePrompt
      }

      console.log(`🧠 找到 ${similarCases.length} 个相似案例，生成智能提示`)

      // 提取成功案例的模式
      const successPatterns = this.extractSuccessPatterns(similarCases)
      
      const smartPrompt = `${basePrompt}

参考成功案例模式：
${successPatterns.map((pattern, index) => `${index + 1}. ${pattern}`).join('\n')}

基于相似案例，请特别注意：
- 项目名称格式：${this.getCommonProjectFormat(similarCases)}
- 楼层提取方式：${this.getCommonFloorFormat(similarCases)}
- 风口类型识别：${this.getCommonVentTypes(similarCases)}
- 尺寸格式处理：${this.getCommonDimensionFormats(similarCases)}

请参考上述成功案例的模式进行识别。`

      return smartPrompt
    } catch (error) {
      console.error('❌ 生成智能提示失败:', error)
      return basePrompt
    }
  }

  /**
   * 清理知识库
   */
  clearKnowledgeBase(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY)
      console.log('🧹 知识库已清理')
    } catch (error) {
      console.error('❌ 清理知识库失败:', error)
    }
  }

  // 私有方法
  private loadCases(): RecognitionCase[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      return stored ? JSON.parse(stored) : []
    } catch (error) {
      console.error('❌ 加载案例失败:', error)
      return []
    }
  }

  private saveCases(cases: RecognitionCase[]): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(cases))
    } catch (error) {
      console.error('❌ 保存案例失败:', error)
    }
  }

  private findSimilarCase(text: string, cases: RecognitionCase[]): RecognitionCase | null {
    for (const case_ of cases) {
      if (this.calculateSimilarity(text, case_.originalText) > 0.9) {
        return case_
      }
    }
    return null
  }

  private updateCase(id: string, recognizedData: any, confidence: number): string {
    const cases = this.loadCases()
    const caseIndex = cases.findIndex(c => c.id === id)
    
    if (caseIndex !== -1) {
      cases[caseIndex].recognizedData = recognizedData
      cases[caseIndex].confidence = Math.max(cases[caseIndex].confidence, confidence)
      this.saveCases(cases)
    }
    
    return id
  }

  private calculateSimilarity(text1: string, text2: string): number {
    // 简单的文本相似度计算
    const normalize = (str: string) => str.toLowerCase().replace(/\s+/g, '').replace(/[^\w\u4e00-\u9fff]/g, '')
    const norm1 = normalize(text1)
    const norm2 = normalize(text2)
    
    if (norm1 === norm2) return 1
    
    const longer = norm1.length > norm2.length ? norm1 : norm2
    const shorter = norm1.length > norm2.length ? norm2 : norm1
    
    if (longer.length === 0) return 1
    
    const editDistance = this.levenshteinDistance(longer, shorter)
    return (longer.length - editDistance) / longer.length
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = []
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          )
        }
      }
    }
    
    return matrix[str2.length][str1.length]
  }

  private extractTags(originalText: string, recognizedData: any): string[] {
    const tags: string[] = []
    
    // 提取项目类型标签
    if (originalText.includes('小区') || originalText.includes('花园')) tags.push('住宅')
    if (originalText.includes('大厦') || originalText.includes('写字楼')) tags.push('商业')
    if (originalText.includes('学校') || originalText.includes('医院')) tags.push('公建')
    
    // 提取风口类型标签
    if (recognizedData.projects) {
      recognizedData.projects.forEach((project: any) => {
        project.floors?.forEach((floor: any) => {
          floor.rooms?.forEach((room: any) => {
            room.vents?.forEach((vent: any) => {
              if (vent.systemType) tags.push(vent.systemType)
            })
          })
        })
      })
    }
    
    return [...new Set(tags)]
  }

  private extractPatterns(originalText: string, recognizedData: any): any {
    const patterns = {
      ventPatterns: [] as string[],
      dimensionPatterns: [] as string[]
    }
    
    // 提取风口模式
    const ventMatches = originalText.match(/[回出进线检][风气修][口]?[：:]/g)
    if (ventMatches) {
      patterns.ventPatterns = [...new Set(ventMatches)]
    }
    
    // 提取尺寸模式
    const dimensionMatches = originalText.match(/\d+[×X✘*x]\d+/g)
    if (dimensionMatches) {
      patterns.dimensionPatterns = [...new Set(dimensionMatches)]
    }
    
    return patterns
  }

  private extractSuccessPatterns(cases: RecognitionCase[]): string[] {
    const patterns: string[] = []
    
    cases.forEach(case_ => {
      patterns.push(`原文："${case_.originalText.substring(0, 50)}..." → 识别置信度：${case_.confidence}`)
    })
    
    return patterns
  }

  private getCommonProjectFormat(cases: RecognitionCase[]): string {
    const formats = cases.map(c => c.recognizedData.projects?.[0]?.projectName).filter(Boolean)
    return formats.length > 0 ? `如"${formats[0]}"` : '项目名称'
  }

  private getCommonFloorFormat(cases: RecognitionCase[]): string {
    const floors = cases.map(c => c.recognizedData.projects?.[0]?.floors?.[0]?.floorName).filter(Boolean)
    return floors.length > 0 ? `如"${floors[0]}"` : '楼层数字'
  }

  private getCommonVentTypes(cases: RecognitionCase[]): string {
    const types = new Set<string>()
    cases.forEach(case_ => {
      case_.recognizedData.projects?.[0]?.floors?.[0]?.rooms?.forEach((room: any) => {
        room.vents?.forEach((vent: any) => {
          if (vent.systemType) types.add(vent.systemType)
        })
      })
    })
    return Array.from(types).slice(0, 3).join('、')
  }

  private getCommonDimensionFormats(cases: RecognitionCase[]): string {
    const formats = new Set<string>()
    cases.forEach(case_ => {
      if (case_.patterns.dimensionPatterns) {
        case_.patterns.dimensionPatterns.forEach(pattern => formats.add(pattern))
      }
    })
    return Array.from(formats).slice(0, 3).join('、')
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 9)
  }
}

export const aiKnowledgeBase = new AIKnowledgeBaseService()
