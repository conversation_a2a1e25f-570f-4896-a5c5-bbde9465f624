/**
 * 简化的主题提供者 - 避免水合错误
 */

'use client'

interface ThemeProviderProps {
  children: React.ReactNode
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  // 完全移除主题初始化逻辑，避免水合错误
  return <>{children}</>
}

// 主题切换Hook
export function useTheme() {
  const {
    mode,
    setMode,
    getCurrentTheme
  } = useThemeStore()

  const toggleTheme = () => {
    const newMode = mode === 'light' ? 'dark' : 'light'
    setMode(newMode)
    // 立即应用主题
    applyThemeManually(newMode)
  }

  const setTheme = (newMode: 'light' | 'dark' | 'auto') => {
    setMode(newMode)
    // 立即应用主题
    applyThemeManually(newMode)
  }

  // 手动应用主题，避免水合错误
  const applyThemeManually = (themeMode: 'light' | 'dark' | 'auto') => {
    if (typeof window === 'undefined') return

    const isSystemDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    const isDark = themeMode === 'dark' || (themeMode === 'auto' && isSystemDark)

    if (isDark) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }

  return {
    mode,
    theme: getCurrentTheme(),
    setTheme,
    toggleTheme
  }
}
