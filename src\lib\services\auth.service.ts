/**
 * 🇨🇳 风口云平台 - 认证服务
 * 
 * ⚠️ 重要提醒：本项目要求所有注释和交流必须使用中文！
 * 
 * 功能说明：
 * - 用户登录认证
 * - 令牌管理
 * - 会话管理
 */

import { api } from '@/lib/api/client'
import { useAuthStore } from '@/lib/store/auth'
import { sessionService } from './session.service'
import type { Admin, FactoryUser, User, isAdmin, isFactoryUser } from '@/types'
import { isJWTExpired, isJWTExpiringSoon } from '@/lib/auth/jwt'

// 动态导入 Token 监控服务以避免循环依赖
let tokenMonitorService: any = null
const getTokenMonitorService = async () => {
  if (!tokenMonitorService) {
    const tokenModule = await import('./token-monitor.service')
    tokenMonitorService = tokenModule.tokenMonitorService
  }
  return tokenMonitorService
}

// 登录请求接口
export interface LoginRequest {
  username: string
  password: string
}

// 登录响应接口
export interface LoginResponse {
  success: boolean
  user: Admin | FactoryUser
  accessToken: string
  refreshToken: string
  tokenType: string
  expiresIn: string
  error?: string
  message?: string
}

/**
 * 认证服务类
 */
export class AuthService {
  /**
   * 工厂用户登录
   */
  static async loginFactory(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await api.post<LoginResponse>('/api/auth/factory', credentials, {
        requireAuth: false
      })

      if (response.success && response.data) {
        const { user, accessToken, refreshToken, tokenType, expiresIn, sessionConflict } = response.data

        // 🔧 修正：只在真正的设备冲突时才显示提示
        if (sessionConflict && sessionConflict.conflictType === 'multi_device' && typeof window !== 'undefined') {
          console.log('⚠️ 检测到多设备登录冲突:', sessionConflict)

          const conflictMessage = `🔒 安全提示：检测到多设备登录\n\n` +
            `${sessionConflict.message}\n\n` +
            `其他设备登录信息：\n` +
            `• 时间：${sessionConflict.previousSession?.loginTime ? new Date(sessionConflict.previousSession.loginTime).toLocaleString() : '未知'}\n` +
            `• IP地址：${sessionConflict.previousSession?.ipAddress || '未知'}\n` +
            `• 设备：${sessionConflict.previousSession?.deviceInfo || '未知'}\n\n` +
            `为保障账号安全，系统已自动踢出其他设备的登录。\n` +
            `同一设备的多个标签页可以正常使用。`

          // 延迟显示提示，确保登录流程完成
          setTimeout(() => {
            alert(conflictMessage)
          }, 1000)
        }

        // 先清理之前的认证状态，防止角色污染（同步清理）
        console.log('🧹 清理之前的认证状态，防止角色污染')
        if (typeof window !== 'undefined') {
          localStorage.removeItem('accessToken')
          localStorage.removeItem('refreshToken')
          localStorage.removeItem('user')
          localStorage.removeItem('role')
          localStorage.removeItem('factoryId')
          localStorage.removeItem('auth-storage')
        }

        // 更新认证状态 - 工厂用户统一使用 'factory' 角色
        useAuthStore.getState().login(
          user,
          'factory', // 工厂用户统一使用 'factory' 角色
          { accessToken, refreshToken, tokenType, expiresIn },
          (user as FactoryUser).factoryId
        )

        // 🔧 简化：移除定期监控服务，只在API请求时验证
        console.log('✅ 登录成功，会话验证将在每次API请求时进行')

        // 🔧 简化：移除激进会话监控，减少不必要的网络请求
        console.log('✅ 会话安全依赖API请求时的验证，无需定期检查')

        // 🔧 暂时禁用标准会话监控，避免chunk加载问题
        console.log('ℹ️ 标准会话监控已禁用，依赖API请求时的验证')

        return response.data
      }

      return {
        success: false,
        error: response.error || '登录失败',
        message: response.message
      } as LoginResponse
    } catch (error) {
      console.error('❌ 工厂用户登录失败:', error)
      return {
        success: false,
        error: '登录服务异常',
        message: error instanceof Error ? error.message : '未知错误'
      } as LoginResponse
    }
  }

  /**
   * 管理员登录
   */
  static async loginAdmin(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await api.post<LoginResponse>('/api/auth/admin-simple', credentials, {
        requireAuth: false
      })

      if (response.success && response.data) {
        const { user, accessToken, refreshToken, tokenType, expiresIn } = response.data

        // 先清理之前的认证状态，防止角色污染（同步清理）
        console.log('🧹 清理之前的认证状态，防止角色污染')
        if (typeof window !== 'undefined') {
          localStorage.removeItem('accessToken')
          localStorage.removeItem('refreshToken')
          localStorage.removeItem('user')
          localStorage.removeItem('role')
          localStorage.removeItem('factoryId')
          localStorage.removeItem('auth-storage')
        }

        // 更新认证状态
        useAuthStore.getState().login(
          user,
          'admin',
          { accessToken, refreshToken, tokenType, expiresIn }
        )

        // 🔧 简化：移除定期会话监控，只在API请求时验证会话
        console.log('✅ 登录成功，会话验证将在API请求时进行')

        return response.data
      }

      return {
        success: false,
        error: response.error || '登录失败',
        message: response.message
      } as LoginResponse
    } catch (error) {
      console.error('❌ 管理员登录失败:', error)
      return {
        success: false,
        error: '登录服务异常',
        message: error instanceof Error ? error.message : '未知错误'
      } as LoginResponse
    }
  }

  /**
   * 用户登出
   */
  static async logout(): Promise<void> {
    try {
      // 🔧 新增：停止 Token 监控
      const tokenMonitor = await getTokenMonitorService()
      tokenMonitor.onUserLogout()

      // 🔧 新增：使用会话服务处理登出
      await sessionService.logout()

      console.log('✅ 用户已登出')
    } catch (error) {
      console.error('❌ 登出失败:', error)
      // 即使登出API失败，也要清除本地状态
      const tokenMonitor = await getTokenMonitorService()
      tokenMonitor.onUserLogout()
      sessionService.onUserLogout()
      useAuthStore.getState().logout()
    }
  }

  /**
   * 检查认证状态
   */
  static isAuthenticated(): boolean {
    const { isAuthenticated, accessToken } = useAuthStore.getState()
    return isAuthenticated && !!accessToken
  }

  /**
   * 检查令牌是否过期
   */
  static isTokenExpired(): boolean {
    const { accessToken } = useAuthStore.getState()
    if (!accessToken) return true
    return isJWTExpired(accessToken)
  }

  /**
   * 检查令牌是否即将过期（30分钟内）
   */
  static isTokenExpiringSoon(): boolean {
    const { accessToken } = useAuthStore.getState()
    if (!accessToken) return true

    return isJWTExpiringSoon(accessToken, 30)
  }

  /**
   * 主动检查并刷新令牌
   */
  static async checkAndRefreshToken(): Promise<boolean> {
    const { isAuthenticated } = useAuthStore.getState()

    if (!isAuthenticated) {
      return false
    }

    // 如果令牌已过期，尝试刷新
    if (this.isTokenExpired()) {
      console.log('🔄 令牌已过期，尝试刷新...')
      return await this.refreshToken()
    }

    // 如果令牌即将过期，主动刷新
    if (this.isTokenExpiringSoon()) {
      console.log('🔄 令牌即将过期，主动刷新...')
      return await this.refreshToken()
    }

    return true
  }

  /**
   * 获取当前用户信息
   */
  static getCurrentUser(): Admin | FactoryUser | null {
    return useAuthStore.getState().user as Admin | FactoryUser | null
  }

  /**
   * 获取当前用户角色
   */
  static getCurrentRole(): string | null {
    return useAuthStore.getState().role
  }

  /**
   * 获取当前工厂ID
   */
  static getCurrentFactoryId(): string | null {
    return useAuthStore.getState().factoryId
  }

  /**
   * 检查用户是否为管理员
   */
  static isAdmin(): boolean {
    const { user } = useAuthStore.getState()
    return Boolean(user && 'username' in user && 'email' in user && !('factoryId' in user))
  }

  /**
   * 检查用户是否为工厂用户
   */
  static isFactoryUser(): boolean {
    const { user } = useAuthStore.getState()
    return Boolean(user && 'factoryId' in user)
  }

  /**
   * 刷新令牌
   */
  static async refreshToken(): Promise<boolean> {
    try {
      const { refreshToken } = useAuthStore.getState()
      
      if (!refreshToken) {
        this.logout()
        return false
      }

      const response = await api.post('/api/auth/refresh', { refreshToken }, {
        requireAuth: false
      })

      if (response.success && response.data) {
        const { accessToken, refreshToken: newRefreshToken, tokenType, expiresIn } = response.data
        
        useAuthStore.getState().updateTokens({
          accessToken,
          refreshToken: newRefreshToken,
          tokenType,
          expiresIn
        })

        return true
      }

      this.logout()
      return false
    } catch (error) {
      console.error('❌ 令牌刷新失败:', error)
      this.logout()
      return false
    }
  }

  /**
   * 获取访问令牌
   */
  static getAccessToken(): string | null {
    return useAuthStore.getState().accessToken
  }

  /**
   * 获取认证头
   */
  static getAuthHeader(): Record<string, string> {
    const { accessToken, tokenType } = useAuthStore.getState()
    
    if (!accessToken || !tokenType) {
      return {}
    }

    return {
      'Authorization': `${tokenType} ${accessToken}`
    }
  }
}

export default AuthService
