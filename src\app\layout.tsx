import type { Metadata } from "next";
import { Geist } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/components/auth/auth-provider";
import { ThemeProvider } from "@/components/theme/theme-provider-simple";
import { ErrorBoundary } from "@/components/error-boundary";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "风口云平台 - 中央空调风口加工厂SaaS管理系统",
  description: "专业的中央空调风口加工厂多工厂管理与数据运营平台，提供客户管理、订单管理、成本分析等一站式解决方案",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body
        className={`${geistSans.variable} antialiased`}
      >
        <ErrorBoundary>
          <ThemeProvider>
            <AuthProvider>
              {children}
            </AuthProvider>
          </ThemeProvider>
        </ErrorBoundary>
        {/* Portal容器 - 确保Portal组件能继承主题 */}
        <div id="portal-root"></div>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // 全局错误处理
              window.addEventListener('unhandledrejection', (event) => {
                if (event.reason?.message?.includes('Copy to clipboard is not supported')) {
                  console.warn('🔧 捕获到未处理的复制错误，已忽略:', event.reason.message);
                  event.preventDefault();
                  return;
                }
              });

              window.addEventListener('error', (event) => {
                if (event.message?.includes('Copy to clipboard is not supported')) {
                  console.warn('🔧 捕获到全局复制错误，已忽略:', event.message);
                  event.preventDefault();
                  return;
                }
              });
            `,
          }}
        />
      </body>
    </html>
  );
}
