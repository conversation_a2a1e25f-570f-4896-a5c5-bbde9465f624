The resource <URL> was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.
The resource <URL> was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.
The resource <URL> was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.
The resource <URL> was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.
The resource <URL> was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.
The resource <URL> was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.
The resource <URL> was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.
The resource <URL> was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.
The resource <URL> was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.
The resource <URL> was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.
 风口信息检测: 关键词=false, 尺寸=false, 总体=false, 文本="江苏·淮安市安豪栅栏有限公司"
 🚫 排除房间类型: "厨房"
 🚫 排除房间类型: "客厅"
 🚫 排除房间类型: "次卧"
 🚫 排除房间类型: "主卧"
 🚫 排除房间类型: "客厅"
 🚫 排除房间类型: "主卧"
 🚫 排除房间类型: "次卧"
 🏗️ 第一行作为备选项目信息: 江苏·淮安市安豪栅栏有限公司
 🔍 处理第2行: "1楼"
 风口信息检测: 关键词=false, 尺寸=false, 总体=false, 文本="1楼"
 🏢 提取行首数字楼层: "1"
 从行中提取楼层信息: "1楼" → 楼层: "1"
 🔍 处理第3行: "厨房"
 风口信息检测: 关键词=false, 尺寸=false, 总体=false, 文本="厨房"
 识别房间: 厨房
 🔍 处理第4行: "客厅"
 风口信息检测: 关键词=false, 尺寸=false, 总体=false, 文本="客厅"
 识别房间: 客厅
 🔍 处理第5行: "次卧"
 风口信息检测: 关键词=false, 尺寸=false, 总体=false, 文本="次卧"
 识别房间: 次卧
 🔍 处理第6行: "主卧"
 风口信息检测: 关键词=false, 尺寸=false, 总体=false, 文本="主卧"
 识别房间: 主卧
 🔍 处理第7行: "2楼"
 风口信息检测: 关键词=false, 尺寸=false, 总体=false, 文本="2楼"
 🏢 提取行首数字楼层: "2"
 从行中提取楼层信息: "2楼" → 楼层: "2"
 🔍 处理第8行: "客厅"
 风口信息检测: 关键词=false, 尺寸=false, 总体=false, 文本="客厅"
 识别房间: 客厅
 🔍 处理第9行: "主卧"
 风口信息检测: 关键词=false, 尺寸=false, 总体=false, 文本="主卧"
 识别房间: 主卧
 🔍 处理第10行: "次卧"
 风口信息检测: 关键词=false, 尺寸=false, 总体=false, 文本="次卧"
 识别房间: 次卧
 🔍 开始后置项目名称检查，当前项目数: 1
   项目1: "江苏·淮安市安豪栅栏有限公司", 楼层数: 0, 风口数: 0
 🔍 项目分析结果:
   - 有风口的项目: 无
   - 有名称的项目: 无
 🎯 解析完成，共识别 1 个项目
 🔄 简单解析结果: Array(1)
 ✅ 简单解析成功，项目数: 1
 📊 解析结果: Object
 🔍 项目1详情: Object
 📊 OCR数据收集完成: Object
 📊 OCR数据收集完成，会话ID: ocr_1753628923094_4x61pgeg3a
 ✅ 解析成功，准备显示预览编辑器...
 📤 准备初始化预览，项目数据: 1 个项目
 🔍 预览状态检查（立即）: Object
 🎯 OCR文字解析成功，识别到 1 个项目，预览编辑器已打开
 🎯 尝试使用智能粘贴解析引擎（简单精准模式）...
 🏗️ 开始智能解析多项目粘贴内容（统一引擎）
 📝 原始内容: 江苏·淮安市安豪栅栏有限公司
一楼厨房出风口125x1435
回风口245x1120
客厅出风口130×2770
回风口243x1750
次卧出风口150x800
回风口245x950
主卧出风口150x900
回风口250x.1020
二楼客厅出凤口175x1300
回风口295x1650
主卧出风口140x1500
回风口250x1500
次卧出风口140x800
同风口245x850

 🔍 处理第1行: "江苏·淮安市安豪栅栏有限公司"
 风口信息检测: 关键词=false, 尺寸=false, 总体=false, 文本="江苏·淮安市安豪栅栏有限公司"
 🚫 排除包含尺寸的房间描述: "二楼客厅出凤口175x1300"
 🚫 排除包含尺寸的房间描述: "同风口245x850"
 🏗️ 第一行作为备选项目信息: 江苏·淮安市安豪栅栏有限公司
 🔍 处理第2行: "一楼厨房出风口125x1435"
 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="一楼厨房出风口125x1435"
 🏢 提取行首中文楼层: "一楼" → "1"
 从行中提取楼层信息: "一楼厨房出风口125x1435" → 楼层: "1"
 🔄 解析连续风口信息: "一楼厨房出风口125x1435"
 🔄 智能预处理多尺寸文本: "一楼厨房出风口125x1435"
 📝 单尺寸文本，无需预处理
 🔄 连续风口信息分割: "一楼厨房出风口125x1435"
 📝 单个风口信息，无需分割
     🔍 连续风口检测: 否 (1个段落)
     🔍 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="一楼厨房出风口125x1435"
     🚫 包含风口信息，跳过房间号提取: "一楼厨房出风口125x1435"
     🔍 解析第1个风口段: "一楼厨房出风口125x1435"
     🔍 解析风口文本: "一楼厨房出风口125x1435"
     🧹 预处理后: "一楼厨房出风口125x1435"
 🔍 多尺寸解析: "一楼厨房出风口125x1435"
 🔧 复杂尺寸预处理: "一楼厨房出风口125x1435"
 🔧 预处理后: "一楼厨房出风口125x1435"
 🔍 使用模式匹配: emoji="(\d+(?:\.\d+)?)✖️(\.?\d+(?:\.\d+)?)", normal="(\d+(?:\.\d+)?)[xX×\*+\-—–/@&:：乘by](\.?\d+(?:\.\d+)?)"
 🔍 处理文本: "一楼厨房出风口125x1435"
 🔍 emoji匹配结果: Array(0)
 🔍 normal匹配结果: Array(1)
 🔍 找到 1 个尺寸: Array(1)
   🔍 处理匹配1: "125x1435" → 分割后: Array(2)
   📊 数值解析: "125" → 125, "1435" → 1435
   🧠 尺寸1: 125mm × 1435mm (大数值推断为mm)
 🔄 单位转换: 125mm → 125mm
 🔄 单位转换: 1435mm → 1435mm
 🔍 智能尺寸识别开始: 输入=125×1435
 📏 检测到特殊规格风口: 宽度125mm (小于标准250mm)
 🔄 智能识别结果: 大值1435作为长度，小值125作为宽度
   ✅ 尺寸1解析结果: 1435×125mm
   📊 添加到结果数组: length=1435, width=125
 🎯 多尺寸解析完成，共解析出 1 个尺寸
     🔍 解析到 1 个尺寸
       🔍 处理第1个尺寸: 1435×125
       🔢 智能数量解析: "一楼厨房出风口125x1435"
       ⚠️ 未找到明确数量信息，使用默认值1
     🎯 当前风口完整文本: "一楼厨房出风口125x1435"
     🔍 分析相关文本提取备注: "一楼厨房出风口125x1435"
 提取带楼层房间描述: "一楼厨房" → 楼层:"一楼" 房间:"厨房"
     🚫 跳过单字符房间: "房" (已有完整房间名称)
     ✨ 组合备注信息: "厨房"
 组合房间+备注信息: "厨房"
 🧹 开始清理备注: "厨房"
 ✅ 保留有意义的房间信息: "厨房"
     ✨ 最终备注: "厨房"
 🔍 风口类型判断开始: 文本="一楼厨房出风口125x1435", 长度=1435, 宽度=125
 ✅ 关键词识别: 出风口
 🔍 风口类型判断: 备注="出风口" 长度=1435 宽度=125
 ✅ 出风口识别: 宽度125mm < 255mm
       ✅ 第1个风口解析结果: double_white_outlet 1435×125 ×1 备注:"厨房"
     🎯 总共解析出 1 个风口
     ✅ 第1个风口段第1个风口解析成功: double_white_outlet 1435×125 备注:"厨房"
     🎯 总共解析出 1 个风口
 💨 识别到 1 个风口
 🏢 使用当前楼层上下文: "1"
 🏢 创建楼层: "1"
     ✨ 使用解析结果备注: "厨房"
 ✅ 添加风口到楼层 "1": double_white_outlet 1435×125 房间:"厨房" 备注:"厨房"
 🔍 处理第3行: "回风口245x1120"
 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="回风口245x1120"
 🔄 解析连续风口信息: "回风口245x1120"
 🔄 智能预处理多尺寸文本: "回风口245x1120"
 📝 单尺寸文本，无需预处理
 🔄 连续风口信息分割: "回风口245x1120"
 📝 单个风口信息，无需分割
     🔍 连续风口检测: 否 (1个段落)
     🔍 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="回风口245x1120"
     🚫 包含风口信息，跳过房间号提取: "回风口245x1120"
     🔍 解析第1个风口段: "回风口245x1120"
     🔍 解析风口文本: "回风口245x1120"
     🧹 预处理后: "回风口245x1120"
 🔍 多尺寸解析: "回风口245x1120"
 🔧 复杂尺寸预处理: "回风口245x1120"
 🔧 预处理后: "回风口245x1120"
 🔍 使用模式匹配: emoji="(\d+(?:\.\d+)?)✖️(\.?\d+(?:\.\d+)?)", normal="(\d+(?:\.\d+)?)[xX×\*+\-—–/@&:：乘by](\.?\d+(?:\.\d+)?)"
 🔍 处理文本: "回风口245x1120"
 🔍 emoji匹配结果: Array(0)
 🔍 normal匹配结果: Array(1)
 🔍 找到 1 个尺寸: Array(1)
   🔍 处理匹配1: "245x1120" → 分割后: Array(2)
   📊 数值解析: "245" → 245, "1120" → 1120
   🧠 尺寸1: 245mm × 1120mm (大数值推断为mm)
 🔄 单位转换: 245mm → 245mm
 🔄 单位转换: 1120mm → 1120mm
 🔍 智能尺寸识别开始: 输入=245×1120
 📏 检测到特殊规格风口: 宽度245mm (小于标准250mm)
 🔄 智能识别结果: 大值1120作为长度，小值245作为宽度
   ✅ 尺寸1解析结果: 1120×245mm
   📊 添加到结果数组: length=1120, width=245
 🎯 多尺寸解析完成，共解析出 1 个尺寸
     🔍 解析到 1 个尺寸
       🔍 处理第1个尺寸: 1120×245
       🔢 智能数量解析: "回风口245x1120"
       ⚠️ 未找到明确数量信息，使用默认值1
     🎯 当前风口完整文本: "回风口245x1120"
     🔍 分析相关文本提取备注: "回风口245x1120"
     ❌ 未找到有意义的备注信息
 🔍 风口类型判断开始: 文本="回风口245x1120", 长度=1120, 宽度=245
 ✅ 关键词识别: 回风口
 🔍 风口类型判断: 备注="回风口" 长度=1120 宽度=245
 ✅ 回风口识别: 备注包含回风关键词
       ✅ 第1个风口解析结果: white_return 1120×245 ×1 备注:""
     🎯 总共解析出 1 个风口
     ✅ 第1个风口段第1个风口解析成功: white_return 1120×245 备注:""
     🎯 总共解析出 1 个风口
 💨 识别到 1 个风口
 🏢 使用当前楼层上下文: "1"
     ✨ 使用解析结果备注: ""
 ✅ 添加风口到楼层 "1": white_return 1120×245 房间:"" 备注:""
 🔍 处理第4行: "客厅出风口130×2770"
 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="客厅出风口130×2770"
 🔄 解析连续风口信息: "客厅出风口130×2770"
 🔄 智能预处理多尺寸文本: "客厅出风口130×2770"
 📝 单尺寸文本，无需预处理
 🔄 连续风口信息分割: "客厅出风口130×2770"
 📝 单个风口信息，无需分割
     🔍 连续风口检测: 否 (1个段落)
     🔍 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="客厅出风口130×2770"
     🚫 包含风口信息，跳过房间号提取: "客厅出风口130×2770"
     🔍 解析第1个风口段: "客厅出风口130×2770"
     🔍 解析风口文本: "客厅出风口130×2770"
     🧹 预处理后: "客厅出风口130×2770"
 🔍 多尺寸解析: "客厅出风口130×2770"
 🔧 复杂尺寸预处理: "客厅出风口130×2770"
 🔧 预处理后: "客厅出风口130×2770"
 🔍 使用模式匹配: emoji="(\d+(?:\.\d+)?)✖️(\.?\d+(?:\.\d+)?)", normal="(\d+(?:\.\d+)?)[xX×\*+\-—–/@&:：乘by](\.?\d+(?:\.\d+)?)"
 🔍 处理文本: "客厅出风口130×2770"
 🔍 emoji匹配结果: Array(0)
 🔍 normal匹配结果: Array(1)
 🔍 找到 1 个尺寸: Array(1)
   🔍 处理匹配1: "130×2770" → 分割后: Array(2)
   📊 数值解析: "130" → 130, "2770" → 2770
   🧠 尺寸1: 130mm × 2770mm (大数值推断为mm)
 🔄 单位转换: 130mm → 130mm
 🔄 单位转换: 2770mm → 2770mm
 🔍 智能尺寸识别开始: 输入=130×2770
 📏 检测到特殊规格风口: 宽度130mm (小于标准250mm)
 🔄 智能识别结果: 大值2770作为长度，小值130作为宽度
   ✅ 尺寸1解析结果: 2770×130mm
   📊 添加到结果数组: length=2770, width=130
 🎯 多尺寸解析完成，共解析出 1 个尺寸
     🔍 解析到 1 个尺寸
       🔍 处理第1个尺寸: 2770×130
       🔢 智能数量解析: "客厅出风口130×2770"
       ⚠️ 未找到明确数量信息，使用默认值1
     🎯 当前风口完整文本: "客厅出风口130×2770"
     🔍 分析相关文本提取备注: "客厅出风口130×2770"
     🏠 提取简单房间类型: "客厅"
     ✨ 组合备注信息: "客厅"
 组合房间+备注信息: "客厅"
 🧹 开始清理备注: "客厅"
 ✅ 保留有意义的房间信息: "客厅"
     ✨ 最终备注: "客厅"
 🔍 风口类型判断开始: 文本="客厅出风口130×2770", 长度=2770, 宽度=130
 ✅ 关键词识别: 出风口
 🔍 风口类型判断: 备注="出风口" 长度=2770 宽度=130
 ✅ 出风口识别: 宽度130mm < 255mm
       ✅ 第1个风口解析结果: double_white_outlet 2770×130 ×1 备注:"客厅"
     🎯 总共解析出 1 个风口
     ✅ 第1个风口段第1个风口解析成功: double_white_outlet 2770×130 备注:"客厅"
     🎯 总共解析出 1 个风口
 💨 识别到 1 个风口
 🏢 使用当前楼层上下文: "1"
     ✨ 使用解析结果备注: "客厅"
 ✅ 添加风口到楼层 "1": double_white_outlet 2770×130 房间:"客厅" 备注:"客厅"
 🔍 处理第5行: "回风口243x1750"
 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="回风口243x1750"
 🔄 解析连续风口信息: "回风口243x1750"
 🔄 智能预处理多尺寸文本: "回风口243x1750"
 📝 单尺寸文本，无需预处理
 🔄 连续风口信息分割: "回风口243x1750"
 📝 单个风口信息，无需分割
     🔍 连续风口检测: 否 (1个段落)
     🔍 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="回风口243x1750"
     🚫 包含风口信息，跳过房间号提取: "回风口243x1750"
     🔍 解析第1个风口段: "回风口243x1750"
     🔍 解析风口文本: "回风口243x1750"
     🧹 预处理后: "回风口243x1750"
 🔍 多尺寸解析: "回风口243x1750"
 🔧 复杂尺寸预处理: "回风口243x1750"
 🔧 预处理后: "回风口243x1750"
 🔍 使用模式匹配: emoji="(\d+(?:\.\d+)?)✖️(\.?\d+(?:\.\d+)?)", normal="(\d+(?:\.\d+)?)[xX×\*+\-—–/@&:：乘by](\.?\d+(?:\.\d+)?)"
 🔍 处理文本: "回风口243x1750"
 🔍 emoji匹配结果: Array(0)
 🔍 normal匹配结果: Array(1)
 🔍 找到 1 个尺寸: Array(1)
   🔍 处理匹配1: "243x1750" → 分割后: Array(2)
   📊 数值解析: "243" → 243, "1750" → 1750
   🧠 尺寸1: 243mm × 1750mm (大数值推断为mm)
 🔄 单位转换: 243mm → 243mm
 🔄 单位转换: 1750mm → 1750mm
 🔍 智能尺寸识别开始: 输入=243×1750
 📏 检测到特殊规格风口: 宽度243mm (小于标准250mm)
 🔄 智能识别结果: 大值1750作为长度，小值243作为宽度
   ✅ 尺寸1解析结果: 1750×243mm
   📊 添加到结果数组: length=1750, width=243
 🎯 多尺寸解析完成，共解析出 1 个尺寸
     🔍 解析到 1 个尺寸
       🔍 处理第1个尺寸: 1750×243
       🔢 智能数量解析: "回风口243x1750"
       ⚠️ 未找到明确数量信息，使用默认值1
     🎯 当前风口完整文本: "回风口243x1750"
     🔍 分析相关文本提取备注: "回风口243x1750"
     ❌ 未找到有意义的备注信息
 🔍 风口类型判断开始: 文本="回风口243x1750", 长度=1750, 宽度=243
 ✅ 关键词识别: 回风口
 🔍 风口类型判断: 备注="回风口" 长度=1750 宽度=243
 ✅ 回风口识别: 备注包含回风关键词
       ✅ 第1个风口解析结果: white_return 1750×243 ×1 备注:""
     🎯 总共解析出 1 个风口
     ✅ 第1个风口段第1个风口解析成功: white_return 1750×243 备注:""
     🎯 总共解析出 1 个风口
 💨 识别到 1 个风口
 🏢 使用当前楼层上下文: "1"
     ✨ 使用解析结果备注: ""
 ✅ 添加风口到楼层 "1": white_return 1750×243 房间:"" 备注:""
 🔍 处理第6行: "次卧出风口150x800"
 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="次卧出风口150x800"
 🔄 解析连续风口信息: "次卧出风口150x800"
 🔄 智能预处理多尺寸文本: "次卧出风口150x800"
 📝 单尺寸文本，无需预处理
 🔄 连续风口信息分割: "次卧出风口150x800"
 📝 单个风口信息，无需分割
     🔍 连续风口检测: 否 (1个段落)
     🔍 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="次卧出风口150x800"
     🚫 包含风口信息，跳过房间号提取: "次卧出风口150x800"
     🔍 解析第1个风口段: "次卧出风口150x800"
     🔍 解析风口文本: "次卧出风口150x800"
     🧹 预处理后: "次卧出风口150x800"
 🔍 多尺寸解析: "次卧出风口150x800"
 🔧 复杂尺寸预处理: "次卧出风口150x800"
 🔧 预处理后: "次卧出风口150x800"
 🔍 使用模式匹配: emoji="(\d+(?:\.\d+)?)✖️(\.?\d+(?:\.\d+)?)", normal="(\d+(?:\.\d+)?)[xX×\*+\-—–/@&:：乘by](\.?\d+(?:\.\d+)?)"
 🔍 处理文本: "次卧出风口150x800"
 🔍 emoji匹配结果: Array(0)
 🔍 normal匹配结果: Array(1)
 🔍 找到 1 个尺寸: Array(1)
   🔍 处理匹配1: "150x800" → 分割后: Array(2)
   📊 数值解析: "150" → 150, "800" → 800
   🧠 尺寸1: 150mm × 800mm (大数值推断为mm)
 🔄 单位转换: 150mm → 150mm
 🔄 单位转换: 800mm → 800mm
 🔍 智能尺寸识别开始: 输入=150×800
 📏 检测到特殊规格风口: 宽度150mm (小于标准250mm)
 🔄 智能识别结果: 大值800作为长度，小值150作为宽度
   ✅ 尺寸1解析结果: 800×150mm
   📊 添加到结果数组: length=800, width=150
 🎯 多尺寸解析完成，共解析出 1 个尺寸
     🔍 解析到 1 个尺寸
       🔍 处理第1个尺寸: 800×150
       🔢 智能数量解析: "次卧出风口150x800"
       ⚠️ 未找到明确数量信息，使用默认值1
     🎯 当前风口完整文本: "次卧出风口150x800"
     🔍 分析相关文本提取备注: "次卧出风口150x800"
     🏠 提取简单房间类型: "次卧"
     🚫 跳过单字符房间: "卧" (已有完整房间名称)
     ✨ 组合备注信息: "次卧"
 组合房间+备注信息: "次卧"
 🧹 开始清理备注: "次卧"
 ✅ 保留有意义的房间信息: "次卧"
     ✨ 最终备注: "次卧"
 🔍 风口类型判断开始: 文本="次卧出风口150x800", 长度=800, 宽度=150
 ✅ 关键词识别: 出风口
 🔍 风口类型判断: 备注="出风口" 长度=800 宽度=150
 ✅ 出风口识别: 宽度150mm < 255mm
       ✅ 第1个风口解析结果: double_white_outlet 800×150 ×1 备注:"次卧"
     🎯 总共解析出 1 个风口
     ✅ 第1个风口段第1个风口解析成功: double_white_outlet 800×150 备注:"次卧"
     🎯 总共解析出 1 个风口
 💨 识别到 1 个风口
 🏢 使用当前楼层上下文: "1"
     ✨ 使用解析结果备注: "次卧"
 ✅ 添加风口到楼层 "1": double_white_outlet 800×150 房间:"次卧" 备注:"次卧"
 🔍 处理第7行: "回风口245x950"
 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="回风口245x950"
 🔄 解析连续风口信息: "回风口245x950"
 🔄 智能预处理多尺寸文本: "回风口245x950"
 📝 单尺寸文本，无需预处理
 🔄 连续风口信息分割: "回风口245x950"
 📝 单个风口信息，无需分割
     🔍 连续风口检测: 否 (1个段落)
     🔍 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="回风口245x950"
     🚫 包含风口信息，跳过房间号提取: "回风口245x950"
     🔍 解析第1个风口段: "回风口245x950"
     🔍 解析风口文本: "回风口245x950"
     🧹 预处理后: "回风口245x950"
 🔍 多尺寸解析: "回风口245x950"
 🔧 复杂尺寸预处理: "回风口245x950"
 🔧 预处理后: "回风口245x950"
 🔍 使用模式匹配: emoji="(\d+(?:\.\d+)?)✖️(\.?\d+(?:\.\d+)?)", normal="(\d+(?:\.\d+)?)[xX×\*+\-—–/@&:：乘by](\.?\d+(?:\.\d+)?)"
 🔍 处理文本: "回风口245x950"
 🔍 emoji匹配结果: Array(0)
 🔍 normal匹配结果: Array(1)
 🔍 找到 1 个尺寸: Array(1)
   🔍 处理匹配1: "245x950" → 分割后: Array(2)
   📊 数值解析: "245" → 245, "950" → 950
   🧠 尺寸1: 245mm × 950mm (大数值推断为mm)
 🔄 单位转换: 245mm → 245mm
 🔄 单位转换: 950mm → 950mm
 🔍 智能尺寸识别开始: 输入=245×950
 📏 检测到特殊规格风口: 宽度245mm (小于标准250mm)
 🔄 智能识别结果: 大值950作为长度，小值245作为宽度
   ✅ 尺寸1解析结果: 950×245mm
   📊 添加到结果数组: length=950, width=245
 🎯 多尺寸解析完成，共解析出 1 个尺寸
     🔍 解析到 1 个尺寸
       🔍 处理第1个尺寸: 950×245
       🔢 智能数量解析: "回风口245x950"
       ⚠️ 未找到明确数量信息，使用默认值1
     🎯 当前风口完整文本: "回风口245x950"
     🔍 分析相关文本提取备注: "回风口245x950"
     ❌ 未找到有意义的备注信息
 🔍 风口类型判断开始: 文本="回风口245x950", 长度=950, 宽度=245
 ✅ 关键词识别: 回风口
 🔍 风口类型判断: 备注="回风口" 长度=950 宽度=245
 ✅ 回风口识别: 备注包含回风关键词
       ✅ 第1个风口解析结果: white_return 950×245 ×1 备注:""
     🎯 总共解析出 1 个风口
     ✅ 第1个风口段第1个风口解析成功: white_return 950×245 备注:""
     🎯 总共解析出 1 个风口
 💨 识别到 1 个风口
 🏢 使用当前楼层上下文: "1"
     ✨ 使用解析结果备注: ""
 ✅ 添加风口到楼层 "1": white_return 950×245 房间:"" 备注:""
 🔍 处理第8行: "主卧出风口150x900"
 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="主卧出风口150x900"
 🔄 解析连续风口信息: "主卧出风口150x900"
 🔄 智能预处理多尺寸文本: "主卧出风口150x900"
 📝 单尺寸文本，无需预处理
 🔄 连续风口信息分割: "主卧出风口150x900"
 📝 单个风口信息，无需分割
     🔍 连续风口检测: 否 (1个段落)
     🔍 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="主卧出风口150x900"
     🚫 包含风口信息，跳过房间号提取: "主卧出风口150x900"
     🔍 解析第1个风口段: "主卧出风口150x900"
     🔍 解析风口文本: "主卧出风口150x900"
     🧹 预处理后: "主卧出风口150x900"
 🔍 多尺寸解析: "主卧出风口150x900"
 🔧 复杂尺寸预处理: "主卧出风口150x900"
 🔧 预处理后: "主卧出风口150x900"
 🔍 使用模式匹配: emoji="(\d+(?:\.\d+)?)✖️(\.?\d+(?:\.\d+)?)", normal="(\d+(?:\.\d+)?)[xX×\*+\-—–/@&:：乘by](\.?\d+(?:\.\d+)?)"
 🔍 处理文本: "主卧出风口150x900"
 🔍 emoji匹配结果: Array(0)
 🔍 normal匹配结果: Array(1)
 🔍 找到 1 个尺寸: Array(1)
   🔍 处理匹配1: "150x900" → 分割后: Array(2)
   📊 数值解析: "150" → 150, "900" → 900
   🧠 尺寸1: 150mm × 900mm (大数值推断为mm)
 🔄 单位转换: 150mm → 150mm
 🔄 单位转换: 900mm → 900mm
 🔍 智能尺寸识别开始: 输入=150×900
 📏 检测到特殊规格风口: 宽度150mm (小于标准250mm)
 🔄 智能识别结果: 大值900作为长度，小值150作为宽度
   ✅ 尺寸1解析结果: 900×150mm
   📊 添加到结果数组: length=900, width=150
 🎯 多尺寸解析完成，共解析出 1 个尺寸
     🔍 解析到 1 个尺寸
       🔍 处理第1个尺寸: 900×150
       🔢 智能数量解析: "主卧出风口150x900"
       ⚠️ 未找到明确数量信息，使用默认值1
     🎯 当前风口完整文本: "主卧出风口150x900"
     🔍 分析相关文本提取备注: "主卧出风口150x900"
     🏠 提取简单房间类型: "主卧"
     🚫 跳过单字符房间: "卧" (已有完整房间名称)
     ✨ 组合备注信息: "主卧"
 组合房间+备注信息: "主卧"
 🧹 开始清理备注: "主卧"
 ✅ 保留有意义的房间信息: "主卧"
     ✨ 最终备注: "主卧"
 🔍 风口类型判断开始: 文本="主卧出风口150x900", 长度=900, 宽度=150
 ✅ 关键词识别: 出风口
 🔍 风口类型判断: 备注="出风口" 长度=900 宽度=150
 ✅ 出风口识别: 宽度150mm < 255mm
       ✅ 第1个风口解析结果: double_white_outlet 900×150 ×1 备注:"主卧"
     🎯 总共解析出 1 个风口
     ✅ 第1个风口段第1个风口解析成功: double_white_outlet 900×150 备注:"主卧"
     🎯 总共解析出 1 个风口
 💨 识别到 1 个风口
 🏢 使用当前楼层上下文: "1"
     ✨ 使用解析结果备注: "主卧"
 ✅ 添加风口到楼层 "1": double_white_outlet 900×150 房间:"主卧" 备注:"主卧"
 🔍 处理第9行: "回风口250x.1020"
 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="回风口250x.1020"
 🔄 解析连续风口信息: "回风口250x.1020"
 🔄 智能预处理多尺寸文本: "回风口250x.1020"
 📝 单尺寸文本，无需预处理
 🔄 连续风口信息分割: "回风口250x.1020"
 📝 单个风口信息，无需分割
     🔍 连续风口检测: 否 (1个段落)
     🔍 风口信息检测: 关键词=true, 尺寸=false, 总体=true, 文本="回风口250x"
     🚫 包含风口信息，跳过房间号提取: "回风口250x"
     🔍 解析第1个风口段: "回风口250x.1020"
     🔍 解析风口文本: "回风口250x.1020"
     🧹 预处理后: "回风口250x.1020"
 🔍 多尺寸解析: "回风口250x.1020"
 🔧 复杂尺寸预处理: "回风口250x.1020"
 🔧 预处理后: "回风口250x.1020"
 🔍 使用模式匹配: emoji="(\d+(?:\.\d+)?)✖️(\.?\d+(?:\.\d+)?)", normal="(\d+(?:\.\d+)?)[xX×\*+\-—–/@&:：乘by](\.?\d+(?:\.\d+)?)"
 🔍 处理文本: "回风口250x.1020"
 🔍 emoji匹配结果: Array(0)
 🔍 normal匹配结果: Array(1)
 🔍 找到 1 个尺寸: Array(1)
   🔍 处理匹配1: "250x.1020" → 分割后: Array(2)
   🔧 修正小数点开头的4位数: ".1020" → "1020"
   📊 数值解析: "250" → 250, "1020" → 1020
   🧠 尺寸1: 250mm × 1020mm (大数值推断为mm)
 🔄 单位转换: 250mm → 250mm
 🔄 单位转换: 1020mm → 1020mm
 🔍 智能尺寸识别开始: 输入=250×1020
 ✅ 长方形风口宽度正常: 250mm (标准范围250-350mm)
 🔄 智能识别结果: 大值1020作为长度，小值250作为宽度
   ✅ 尺寸1解析结果: 1020×250mm
   📊 添加到结果数组: length=1020, width=250
 🎯 多尺寸解析完成，共解析出 1 个尺寸
     🔍 解析到 1 个尺寸
       🔍 处理第1个尺寸: 1020×250
       🔢 智能数量解析: "回风口250x.1020"
       ⚠️ 未找到明确数量信息，使用默认值1
     🎯 当前风口完整文本: "回风口250x.1020"
     🔍 分析相关文本提取备注: "回风口250x.1020"
     ❌ 未找到有意义的备注信息
 🔍 风口类型判断开始: 文本="回风口250x.1020", 长度=1020, 宽度=250
 ✅ 关键词识别: 回风口
 🔍 风口类型判断: 备注="回风口" 长度=1020 宽度=250
 ✅ 回风口识别: 备注包含回风关键词
       ✅ 第1个风口解析结果: white_return 1020×250 ×1 备注:""
     🎯 总共解析出 1 个风口
     ✅ 第1个风口段第1个风口解析成功: white_return 1020×250 备注:""
     🎯 总共解析出 1 个风口
 💨 识别到 1 个风口
 🏢 使用当前楼层上下文: "1"
     ✨ 使用解析结果备注: ""
 ✅ 添加风口到楼层 "1": white_return 1020×250 房间:"" 备注:""
 🔍 处理第10行: "二楼客厅出凤口175x1300"
 风口信息检测: 关键词=false, 尺寸=true, 总体=true, 文本="二楼客厅出凤口175x1300"
 🏢 提取行首中文楼层: "二楼" → "2"
 从行中提取楼层信息: "二楼客厅出凤口175x1300" → 楼层: "2"
 🔄 解析连续风口信息: "二楼客厅出凤口175x1300"
 🔄 智能预处理多尺寸文本: "二楼客厅出凤口175x1300"
 📝 单尺寸文本，无需预处理
 🔄 连续风口信息分割: "二楼客厅出凤口175x1300"
 📝 单个风口信息，无需分割
     🔍 连续风口检测: 否 (1个段落)
     🔍 风口信息检测: 关键词=false, 尺寸=true, 总体=true, 文本="二楼客厅出凤口175x1300"
     🚫 包含风口信息，跳过房间号提取: "二楼客厅出凤口175x1300"
     🔍 解析第1个风口段: "二楼客厅出凤口175x1300"
     🔍 解析风口文本: "二楼客厅出凤口175x1300"
     🧹 预处理后: "二楼客厅出凤口175x1300"
 🔍 多尺寸解析: "二楼客厅出凤口175x1300"
 🔧 复杂尺寸预处理: "二楼客厅出凤口175x1300"
 🔧 预处理后: "二楼客厅出凤口175x1300"
 🔍 使用模式匹配: emoji="(\d+(?:\.\d+)?)✖️(\.?\d+(?:\.\d+)?)", normal="(\d+(?:\.\d+)?)[xX×\*+\-—–/@&:：乘by](\.?\d+(?:\.\d+)?)"
 🔍 处理文本: "二楼客厅出凤口175x1300"
 🔍 emoji匹配结果: Array(0)
 🔍 normal匹配结果: Array(1)
 🔍 找到 1 个尺寸: Array(1)
   🔍 处理匹配1: "175x1300" → 分割后: Array(2)
   📊 数值解析: "175" → 175, "1300" → 1300
   🧠 尺寸1: 175mm × 1300mm (大数值推断为mm)
 🔄 单位转换: 175mm → 175mm
 🔄 单位转换: 1300mm → 1300mm
 🔍 智能尺寸识别开始: 输入=175×1300
 📏 检测到特殊规格风口: 宽度175mm (小于标准250mm)
 🔄 智能识别结果: 大值1300作为长度，小值175作为宽度
   ✅ 尺寸1解析结果: 1300×175mm
   📊 添加到结果数组: length=1300, width=175
 🎯 多尺寸解析完成，共解析出 1 个尺寸
     🔍 解析到 1 个尺寸
       🔍 处理第1个尺寸: 1300×175
       🔢 智能数量解析: "二楼客厅出凤口175x1300"
       ⚠️ 未找到明确数量信息，使用默认值1
     🎯 当前风口完整文本: "二楼客厅出凤口175x1300"
     🔍 分析相关文本提取备注: "二楼客厅出凤口175x1300"
 提取带楼层房间描述: "二楼客厅" → 楼层:"二楼" 房间:"客厅"
     ✨ 组合备注信息: "客厅"
 组合房间+备注信息: "客厅"
 🧹 开始清理备注: "客厅"
 ✅ 保留有意义的房间信息: "客厅"
     ✨ 最终备注: "客厅"
 🔍 风口类型判断开始: 文本="二楼客厅出凤口175x1300", 长度=1300, 宽度=175
 🔍 基于尺寸判断: 最终长度=1300, 最终宽度=175
 ✅ 宽度判断: 出风口 (宽度175mm 在160-255mm之间)
 🔍 风口类型判断: 备注="出风口" 长度=1300 宽度=175
 ✅ 出风口识别: 宽度175mm < 255mm
       ✅ 第1个风口解析结果: double_white_outlet 1300×175 ×1 备注:"客厅"
     🎯 总共解析出 1 个风口
     ✅ 第1个风口段第1个风口解析成功: double_white_outlet 1300×175 备注:"客厅"
     🎯 总共解析出 1 个风口
 💨 识别到 1 个风口
 🏢 使用当前楼层上下文: "2"
 🏢 创建楼层: "2"
     ✨ 使用解析结果备注: "客厅"
 ✅ 添加风口到楼层 "2": double_white_outlet 1300×175 房间:"客厅" 备注:"客厅"
 🔍 处理第11行: "回风口295x1650"
 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="回风口295x1650"
 🔄 解析连续风口信息: "回风口295x1650"
 🔄 智能预处理多尺寸文本: "回风口295x1650"
 📝 单尺寸文本，无需预处理
 🔄 连续风口信息分割: "回风口295x1650"
 📝 单个风口信息，无需分割
     🔍 连续风口检测: 否 (1个段落)
     🔍 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="回风口295x1650"
     🚫 包含风口信息，跳过房间号提取: "回风口295x1650"
     🔍 解析第1个风口段: "回风口295x1650"
     🔍 解析风口文本: "回风口295x1650"
     🧹 预处理后: "回风口295x1650"
 🔍 多尺寸解析: "回风口295x1650"
 🔧 复杂尺寸预处理: "回风口295x1650"
 🔧 预处理后: "回风口295x1650"
 🔍 使用模式匹配: emoji="(\d+(?:\.\d+)?)✖️(\.?\d+(?:\.\d+)?)", normal="(\d+(?:\.\d+)?)[xX×\*+\-—–/@&:：乘by](\.?\d+(?:\.\d+)?)"
 🔍 处理文本: "回风口295x1650"
 🔍 emoji匹配结果: Array(0)
 🔍 normal匹配结果: Array(1)
 🔍 找到 1 个尺寸: Array(1)
   🔍 处理匹配1: "295x1650" → 分割后: Array(2)
   📊 数值解析: "295" → 295, "1650" → 1650
   🧠 尺寸1: 295mm × 1650mm (大数值推断为mm)
 🔄 单位转换: 295mm → 295mm
 🔄 单位转换: 1650mm → 1650mm
 🔍 智能尺寸识别开始: 输入=295×1650
 ✅ 长方形风口宽度正常: 295mm (标准范围250-350mm)
 🔄 智能识别结果: 大值1650作为长度，小值295作为宽度
   ✅ 尺寸1解析结果: 1650×295mm
   📊 添加到结果数组: length=1650, width=295
 🎯 多尺寸解析完成，共解析出 1 个尺寸
     🔍 解析到 1 个尺寸
       🔍 处理第1个尺寸: 1650×295
       🔢 智能数量解析: "回风口295x1650"
       ⚠️ 未找到明确数量信息，使用默认值1
     🎯 当前风口完整文本: "回风口295x1650"
     🔍 分析相关文本提取备注: "回风口295x1650"
     ❌ 未找到有意义的备注信息
 🔍 风口类型判断开始: 文本="回风口295x1650", 长度=1650, 宽度=295
 ✅ 关键词识别: 回风口
 🔍 风口类型判断: 备注="回风口" 长度=1650 宽度=295
 ✅ 回风口识别: 备注包含回风关键词
       ✅ 第1个风口解析结果: white_return 1650×295 ×1 备注:""
     🎯 总共解析出 1 个风口
     ✅ 第1个风口段第1个风口解析成功: white_return 1650×295 备注:""
     🎯 总共解析出 1 个风口
 💨 识别到 1 个风口
 🏢 使用当前楼层上下文: "2"
     ✨ 使用解析结果备注: ""
 ✅ 添加风口到楼层 "2": white_return 1650×295 房间:"" 备注:""
 🔍 处理第12行: "主卧出风口140x1500"
 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="主卧出风口140x1500"
 🔄 解析连续风口信息: "主卧出风口140x1500"
 🔄 智能预处理多尺寸文本: "主卧出风口140x1500"
 📝 单尺寸文本，无需预处理
 🔄 连续风口信息分割: "主卧出风口140x1500"
 📝 单个风口信息，无需分割
     🔍 连续风口检测: 否 (1个段落)
     🔍 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="主卧出风口140x1500"
     🚫 包含风口信息，跳过房间号提取: "主卧出风口140x1500"
     🔍 解析第1个风口段: "主卧出风口140x1500"
     🔍 解析风口文本: "主卧出风口140x1500"
     🧹 预处理后: "主卧出风口140x1500"
 🔍 多尺寸解析: "主卧出风口140x1500"
 🔧 复杂尺寸预处理: "主卧出风口140x1500"
 🔧 预处理后: "主卧出风口140x1500"
 🔍 使用模式匹配: emoji="(\d+(?:\.\d+)?)✖️(\.?\d+(?:\.\d+)?)", normal="(\d+(?:\.\d+)?)[xX×\*+\-—–/@&:：乘by](\.?\d+(?:\.\d+)?)"
 🔍 处理文本: "主卧出风口140x1500"
 🔍 emoji匹配结果: Array(0)
 🔍 normal匹配结果: Array(1)
 🔍 找到 1 个尺寸: Array(1)
   🔍 处理匹配1: "140x1500" → 分割后: Array(2)
   📊 数值解析: "140" → 140, "1500" → 1500
   🧠 尺寸1: 140mm × 1500mm (大数值推断为mm)
 🔄 单位转换: 140mm → 140mm
 🔄 单位转换: 1500mm → 1500mm
 🔍 智能尺寸识别开始: 输入=140×1500
 📏 检测到特殊规格风口: 宽度140mm (小于标准250mm)
 🔄 智能识别结果: 大值1500作为长度，小值140作为宽度
   ✅ 尺寸1解析结果: 1500×140mm
   📊 添加到结果数组: length=1500, width=140
 🎯 多尺寸解析完成，共解析出 1 个尺寸
     🔍 解析到 1 个尺寸
       🔍 处理第1个尺寸: 1500×140
       🔢 智能数量解析: "主卧出风口140x1500"
       ⚠️ 未找到明确数量信息，使用默认值1
     🎯 当前风口完整文本: "主卧出风口140x1500"
     🔍 分析相关文本提取备注: "主卧出风口140x1500"
     🏠 提取简单房间类型: "主卧"
     🚫 跳过单字符房间: "卧" (已有完整房间名称)
     ✨ 组合备注信息: "主卧"
 组合房间+备注信息: "主卧"
 🧹 开始清理备注: "主卧"
 ✅ 保留有意义的房间信息: "主卧"
     ✨ 最终备注: "主卧"
 🔍 风口类型判断开始: 文本="主卧出风口140x1500", 长度=1500, 宽度=140
 ✅ 关键词识别: 出风口
 🔍 风口类型判断: 备注="出风口" 长度=1500 宽度=140
 ✅ 出风口识别: 宽度140mm < 255mm
       ✅ 第1个风口解析结果: double_white_outlet 1500×140 ×1 备注:"主卧"
     🎯 总共解析出 1 个风口
     ✅ 第1个风口段第1个风口解析成功: double_white_outlet 1500×140 备注:"主卧"
     🎯 总共解析出 1 个风口
 💨 识别到 1 个风口
 🏢 使用当前楼层上下文: "2"
     ✨ 使用解析结果备注: "主卧"
 ✅ 添加风口到楼层 "2": double_white_outlet 1500×140 房间:"主卧" 备注:"主卧"
 🔍 处理第13行: "回风口250x1500"
 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="回风口250x1500"
 🔄 解析连续风口信息: "回风口250x1500"
 🔄 智能预处理多尺寸文本: "回风口250x1500"
 📝 单尺寸文本，无需预处理
 🔄 连续风口信息分割: "回风口250x1500"
 📝 单个风口信息，无需分割
     🔍 连续风口检测: 否 (1个段落)
     🔍 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="回风口250x1500"
     🚫 包含风口信息，跳过房间号提取: "回风口250x1500"
     🔍 解析第1个风口段: "回风口250x1500"
     🔍 解析风口文本: "回风口250x1500"
     🧹 预处理后: "回风口250x1500"
 🔍 多尺寸解析: "回风口250x1500"
 🔧 复杂尺寸预处理: "回风口250x1500"
 🔧 预处理后: "回风口250x1500"
 🔍 使用模式匹配: emoji="(\d+(?:\.\d+)?)✖️(\.?\d+(?:\.\d+)?)", normal="(\d+(?:\.\d+)?)[xX×\*+\-—–/@&:：乘by](\.?\d+(?:\.\d+)?)"
 🔍 处理文本: "回风口250x1500"
 🔍 emoji匹配结果: Array(0)
 🔍 normal匹配结果: Array(1)
 🔍 找到 1 个尺寸: Array(1)
   🔍 处理匹配1: "250x1500" → 分割后: Array(2)
   📊 数值解析: "250" → 250, "1500" → 1500
   🧠 尺寸1: 250mm × 1500mm (大数值推断为mm)
 🔄 单位转换: 250mm → 250mm
 🔄 单位转换: 1500mm → 1500mm
 🔍 智能尺寸识别开始: 输入=250×1500
 ✅ 长方形风口宽度正常: 250mm (标准范围250-350mm)
 🔄 智能识别结果: 大值1500作为长度，小值250作为宽度
   ✅ 尺寸1解析结果: 1500×250mm
   📊 添加到结果数组: length=1500, width=250
 🎯 多尺寸解析完成，共解析出 1 个尺寸
     🔍 解析到 1 个尺寸
       🔍 处理第1个尺寸: 1500×250
       🔢 智能数量解析: "回风口250x1500"
       ⚠️ 未找到明确数量信息，使用默认值1
     🎯 当前风口完整文本: "回风口250x1500"
     🔍 分析相关文本提取备注: "回风口250x1500"
     ❌ 未找到有意义的备注信息
 🔍 风口类型判断开始: 文本="回风口250x1500", 长度=1500, 宽度=250
 ✅ 关键词识别: 回风口
 🔍 风口类型判断: 备注="回风口" 长度=1500 宽度=250
 ✅ 回风口识别: 备注包含回风关键词
       ✅ 第1个风口解析结果: white_return 1500×250 ×1 备注:""
     🎯 总共解析出 1 个风口
     ✅ 第1个风口段第1个风口解析成功: white_return 1500×250 备注:""
     🎯 总共解析出 1 个风口
 💨 识别到 1 个风口
 🏢 使用当前楼层上下文: "2"
     ✨ 使用解析结果备注: ""
 ✅ 添加风口到楼层 "2": white_return 1500×250 房间:"" 备注:""
 🔍 处理第14行: "次卧出风口140x800"
 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="次卧出风口140x800"
 🔄 解析连续风口信息: "次卧出风口140x800"
 🔄 智能预处理多尺寸文本: "次卧出风口140x800"
 📝 单尺寸文本，无需预处理
 🔄 连续风口信息分割: "次卧出风口140x800"
 📝 单个风口信息，无需分割
     🔍 连续风口检测: 否 (1个段落)
     🔍 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="次卧出风口140x800"
     🚫 包含风口信息，跳过房间号提取: "次卧出风口140x800"
     🔍 解析第1个风口段: "次卧出风口140x800"
     🔍 解析风口文本: "次卧出风口140x800"
     🧹 预处理后: "次卧出风口140x800"
 🔍 多尺寸解析: "次卧出风口140x800"
 🔧 复杂尺寸预处理: "次卧出风口140x800"
 🔧 预处理后: "次卧出风口140x800"
 🔍 使用模式匹配: emoji="(\d+(?:\.\d+)?)✖️(\.?\d+(?:\.\d+)?)", normal="(\d+(?:\.\d+)?)[xX×\*+\-—–/@&:：乘by](\.?\d+(?:\.\d+)?)"
 🔍 处理文本: "次卧出风口140x800"
 🔍 emoji匹配结果: Array(0)
 🔍 normal匹配结果: Array(1)
 🔍 找到 1 个尺寸: Array(1)
   🔍 处理匹配1: "140x800" → 分割后: Array(2)
   📊 数值解析: "140" → 140, "800" → 800
   🧠 尺寸1: 140mm × 800mm (大数值推断为mm)
 🔄 单位转换: 140mm → 140mm
 🔄 单位转换: 800mm → 800mm
 🔍 智能尺寸识别开始: 输入=140×800
 📏 检测到特殊规格风口: 宽度140mm (小于标准250mm)
 🔄 智能识别结果: 大值800作为长度，小值140作为宽度
   ✅ 尺寸1解析结果: 800×140mm
   📊 添加到结果数组: length=800, width=140
 🎯 多尺寸解析完成，共解析出 1 个尺寸
     🔍 解析到 1 个尺寸
       🔍 处理第1个尺寸: 800×140
       🔢 智能数量解析: "次卧出风口140x800"
       ⚠️ 未找到明确数量信息，使用默认值1
     🎯 当前风口完整文本: "次卧出风口140x800"
     🔍 分析相关文本提取备注: "次卧出风口140x800"
     🏠 提取简单房间类型: "次卧"
     🚫 跳过单字符房间: "卧" (已有完整房间名称)
     ✨ 组合备注信息: "次卧"
 组合房间+备注信息: "次卧"
 🧹 开始清理备注: "次卧"
 ✅ 保留有意义的房间信息: "次卧"
     ✨ 最终备注: "次卧"
 🔍 风口类型判断开始: 文本="次卧出风口140x800", 长度=800, 宽度=140
 ✅ 关键词识别: 出风口
 🔍 风口类型判断: 备注="出风口" 长度=800 宽度=140
 ✅ 出风口识别: 宽度140mm < 255mm
       ✅ 第1个风口解析结果: double_white_outlet 800×140 ×1 备注:"次卧"
     🎯 总共解析出 1 个风口
     ✅ 第1个风口段第1个风口解析成功: double_white_outlet 800×140 备注:"次卧"
     🎯 总共解析出 1 个风口
 💨 识别到 1 个风口
 🏢 使用当前楼层上下文: "2"
     ✨ 使用解析结果备注: "次卧"
 ✅ 添加风口到楼层 "2": double_white_outlet 800×140 房间:"次卧" 备注:"次卧"
 🔍 处理第15行: "同风口245x850"
 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="同风口245x850"
 🔄 解析连续风口信息: "同风口245x850"
 🔄 智能预处理多尺寸文本: "同风口245x850"
 📝 单尺寸文本，无需预处理
 🔄 连续风口信息分割: "同风口245x850"
 📝 单个风口信息，无需分割
     🔍 连续风口检测: 否 (1个段落)
     🔍 风口信息检测: 关键词=true, 尺寸=true, 总体=true, 文本="同风口245x850"
     🚫 包含风口信息，跳过房间号提取: "同风口245x850"
     🔍 解析第1个风口段: "同风口245x850"
     🔍 解析风口文本: "同风口245x850"
     🧹 预处理后: "同风口245x850"
 🔍 多尺寸解析: "同风口245x850"
 🔧 复杂尺寸预处理: "同风口245x850"
 🔧 预处理后: "同风口245x850"
 🔍 使用模式匹配: emoji="(\d+(?:\.\d+)?)✖️(\.?\d+(?:\.\d+)?)", normal="(\d+(?:\.\d+)?)[xX×\*+\-—–/@&:：乘by](\.?\d+(?:\.\d+)?)"
 🔍 处理文本: "同风口245x850"
 🔍 emoji匹配结果: Array(0)
 🔍 normal匹配结果: Array(1)
 🔍 找到 1 个尺寸: Array(1)
   🔍 处理匹配1: "245x850" → 分割后: Array(2)
   📊 数值解析: "245" → 245, "850" → 850
   🧠 尺寸1: 245mm × 850mm (大数值推断为mm)
 🔄 单位转换: 245mm → 245mm
 🔄 单位转换: 850mm → 850mm
 🔍 智能尺寸识别开始: 输入=245×850
 📏 检测到特殊规格风口: 宽度245mm (小于标准250mm)
 🔄 智能识别结果: 大值850作为长度，小值245作为宽度
   ✅ 尺寸1解析结果: 850×245mm
   📊 添加到结果数组: length=850, width=245
 🎯 多尺寸解析完成，共解析出 1 个尺寸
     🔍 解析到 1 个尺寸
       🔍 处理第1个尺寸: 850×245
       🔢 智能数量解析: "同风口245x850"
       ⚠️ 未找到明确数量信息，使用默认值1
     🎯 当前风口完整文本: "同风口245x850"
     🔍 分析相关文本提取备注: "同风口245x850"
     ❌ 未找到有意义的备注信息
 🔍 风口类型判断开始: 文本="同风口245x850", 长度=850, 宽度=245
 🔍 基于尺寸判断: 最终长度=850, 最终宽度=245
 ✅ 宽度判断: 出风口 (宽度245mm 在160-255mm之间)
 🔍 风口类型判断: 备注="出风口" 长度=850 宽度=245
 ✅ 出风口识别: 宽度245mm < 255mm
       ✅ 第1个风口解析结果: double_white_outlet 850×245 ×1 备注:""
     🎯 总共解析出 1 个风口
     ✅ 第1个风口段第1个风口解析成功: double_white_outlet 850×245 备注:""
     🎯 总共解析出 1 个风口
 💨 识别到 1 个风口
 🏢 使用当前楼层上下文: "2"
     ✨ 使用解析结果备注: ""
 ✅ 添加风口到楼层 "2": double_white_outlet 850×245 房间:"" 备注:""
 🔍 开始后置项目名称检查，当前项目数: 1
   项目1: "江苏·淮安市安豪栅栏有限公司", 楼层数: 2, 风口数: 14
 🔍 项目分析结果:
   - 有风口的项目: 无
   - 有名称的项目: 无
 🎯 解析完成，共识别 1 个项目
 🔄 简单解析结果: Array(1)
 ✅ 简单解析成功，项目数: 1
 📊 解析结果: Object
 🔍 项目1详情: Object
   📁 楼层1: undefined, 风口数量: 8
     🌪️ 风口1: double_white_outlet 1435x125 数量:1 备注:"厨房"
     🌪️ 风口2: white_return 1120x245 数量:1 备注:""
     🌪️ 风口3: double_white_outlet 2770x130 数量:1 备注:"客厅"
     ... 还有 5 个风口
   📁 楼层2: undefined, 风口数量: 6
     🌪️ 风口1: double_white_outlet 1300x175 数量:1 备注:"客厅"
     🌪️ 风口2: white_return 1650x295 数量:1 备注:""
     🌪️ 风口3: double_white_outlet 1500x140 数量:1 备注:"主卧"
     ... 还有 3 个风口
 📊 OCR数据收集完成: Object
 📊 OCR数据收集完成，会话ID: ocr_1753628924284_vfhq5gt2xj
 ✅ 解析成功，准备显示预览编辑器...
 📤 准备初始化预览，项目数据: 1 个项目
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=1435×125
 📏 检测到特殊规格风口: 宽度125mm (小于标准250mm)
 🔄 智能识别结果: 大值1435作为长度，小值125作为宽度
 🔍 [预览编辑器] 使用默认单价: double_white_outlet -> 130
 🔧 [预览编辑器] 单价处理: 原始=0, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.276575㎡, 计算=0.276575 × 130 × 1 = 35.954750000000004
 🔧 [价格计算] 最终价格: 35.954750000000004 → 36
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=1120×245
 📏 检测到特殊规格风口: 宽度245mm (小于标准250mm)
 🔄 智能识别结果: 大值1120作为长度，小值245作为宽度
 🔍 [预览编辑器] 使用默认单价: white_return -> 130
 🔧 [预览编辑器] 单价处理: 原始=0, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.359900㎡, 计算=0.3599 × 130 × 1 = 46.787
 🔧 [价格计算] 最终价格: 46.787 → 46.8
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=2770×130
 📏 检测到特殊规格风口: 宽度130mm (小于标准250mm)
 🔄 智能识别结果: 大值2770作为长度，小值130作为宽度
 🔍 [预览编辑器] 使用默认单价: double_white_outlet -> 130
 🔧 [预览编辑器] 单价处理: 原始=0, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.537700㎡, 计算=0.5377 × 130 × 1 = 69.901
 🔧 [价格计算] 最终价格: 69.901 → 69.9
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=1750×243
 📏 检测到特殊规格风口: 宽度243mm (小于标准250mm)
 🔄 智能识别结果: 大值1750作为长度，小值243作为宽度
 🔍 [预览编辑器] 使用默认单价: white_return -> 130
 🔧 [预览编辑器] 单价处理: 原始=0, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.548430㎡, 计算=0.54843 × 130 × 1 = 71.2959
 🔧 [价格计算] 最终价格: 71.2959 → 71.3
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=800×150
 📏 检测到特殊规格风口: 宽度150mm (小于标准250mm)
 🔄 智能识别结果: 大值800作为长度，小值150作为宽度
 🔍 [预览编辑器] 使用默认单价: double_white_outlet -> 130
 🔧 [预览编辑器] 单价处理: 原始=0, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.180600㎡, 计算=0.1806 × 130 × 1 = 23.478
 🔧 [价格计算] 最终价格: 23.478 → 23.5
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=950×245
 📏 检测到特殊规格风口: 宽度245mm (小于标准250mm)
 🔄 智能识别结果: 大值950作为长度，小值245作为宽度
 🔍 [预览编辑器] 使用默认单价: white_return -> 130
 🔧 [预览编辑器] 单价处理: 原始=0, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.308050㎡, 计算=0.30805 × 130 × 1 = 40.0465
 🔧 [价格计算] 最终价格: 40.0465 → 40
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=900×150
 📏 检测到特殊规格风口: 宽度150mm (小于标准250mm)
 🔄 智能识别结果: 大值900作为长度，小值150作为宽度
 🔍 [预览编辑器] 使用默认单价: double_white_outlet -> 130
 🔧 [预览编辑器] 单价处理: 原始=0, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.201600㎡, 计算=0.2016 × 130 × 1 = 26.208
 🔧 [价格计算] 最终价格: 26.208 → 26.2
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=1020×250
 ✅ 长方形风口宽度正常: 250mm (标准范围250-350mm)
 🔄 智能识别结果: 大值1020作为长度，小值250作为宽度
 🔍 [预览编辑器] 使用默认单价: white_return -> 130
 🔧 [预览编辑器] 单价处理: 原始=0, 最终=130
 🔧 [价格计算] 输入参数: Object
 ✅ [价格计算] 常规风口: 面积=0.334800㎡, 计算=0.3348 × 130 × 1 = 43.524
 🔧 [价格计算] 最终价格: 43.524 → 43.5
 🔧 [楼层总价] 楼层"1"总价计算: 8个风口, 总价=357.2
 🔧 [预览编辑器] 转换风口数据: Object
 🔍 智能尺寸识别开始: 输入=1300×175
 📏 检测到特殊规格风口: 宽度175mm (小于标准250mm)
 🔄 智能识别结果: 大值1300作为长度，小值175作为宽度
 🔍 [预览编辑器] 使用默认单价: double_white_outlet -> 130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:210 🔧 [预览编辑器] 单价处理: 原始=0, 最终=130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:21 🔧 [价格计算] 输入参数: Object
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:42 ✅ [价格计算] 常规风口: 面积=0.319600㎡, 计算=0.3196 × 130 × 1 = 41.548
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:59 🔧 [价格计算] 最终价格: 41.548 → 41.5
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:204 🔧 [预览编辑器] 转换风口数据: Object
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:56 🔍 智能尺寸识别开始: 输入=1650×295
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:95 ✅ 长方形风口宽度正常: 295mm (标准范围250-350mm)
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:101 🔄 智能识别结果: 大值1650作为长度，小值295作为宽度
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:198 🔍 [预览编辑器] 使用默认单价: white_return -> 130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:210 🔧 [预览编辑器] 单价处理: 原始=0, 最终=130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:21 🔧 [价格计算] 输入参数: Object
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:42 ✅ [价格计算] 常规风口: 面积=0.607050㎡, 计算=0.60705 × 130 × 1 = 78.9165
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:59 🔧 [价格计算] 最终价格: 78.9165 → 78.9
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:204 🔧 [预览编辑器] 转换风口数据: Object
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:56 🔍 智能尺寸识别开始: 输入=1500×140
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:97 📏 检测到特殊规格风口: 宽度140mm (小于标准250mm)
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:101 🔄 智能识别结果: 大值1500作为长度，小值140作为宽度
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:198 🔍 [预览编辑器] 使用默认单价: double_white_outlet -> 130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:210 🔧 [预览编辑器] 单价处理: 原始=0, 最终=130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:21 🔧 [价格计算] 输入参数: Object
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:42 ✅ [价格计算] 常规风口: 面积=0.312000㎡, 计算=0.312 × 130 × 1 = 40.56
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:59 🔧 [价格计算] 最终价格: 40.56 → 40.6
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:204 🔧 [预览编辑器] 转换风口数据: Object
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:56 🔍 智能尺寸识别开始: 输入=1500×250
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:95 ✅ 长方形风口宽度正常: 250mm (标准范围250-350mm)
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:101 🔄 智能识别结果: 大值1500作为长度，小值250作为宽度
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:198 🔍 [预览编辑器] 使用默认单价: white_return -> 130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:210 🔧 [预览编辑器] 单价处理: 原始=0, 最终=130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:21 🔧 [价格计算] 输入参数: Object
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:42 ✅ [价格计算] 常规风口: 面积=0.483600㎡, 计算=0.4836 × 130 × 1 = 62.867999999999995
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:59 🔧 [价格计算] 最终价格: 62.867999999999995 → 62.9
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:204 🔧 [预览编辑器] 转换风口数据: Object
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:56 🔍 智能尺寸识别开始: 输入=800×140
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:97 📏 检测到特殊规格风口: 宽度140mm (小于标准250mm)
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:101 🔄 智能识别结果: 大值800作为长度，小值140作为宽度
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:198 🔍 [预览编辑器] 使用默认单价: double_white_outlet -> 130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:210 🔧 [预览编辑器] 单价处理: 原始=0, 最终=130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:21 🔧 [价格计算] 输入参数: Object
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:42 ✅ [价格计算] 常规风口: 面积=0.172000㎡, 计算=0.172 × 130 × 1 = 22.36
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:59 🔧 [价格计算] 最终价格: 22.36 → 22.4
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:204 🔧 [预览编辑器] 转换风口数据: Object
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:56 🔍 智能尺寸识别开始: 输入=850×245
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:97 📏 检测到特殊规格风口: 宽度245mm (小于标准250mm)
D:\personal\2025Projects\factorysystem\src\lib\utils\dimension-utils.ts:101 🔄 智能识别结果: 大值850作为长度，小值245作为宽度
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:198 🔍 [预览编辑器] 使用默认单价: double_white_outlet -> 130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:210 🔧 [预览编辑器] 单价处理: 原始=0, 最终=130
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:21 🔧 [价格计算] 输入参数: Object
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:42 ✅ [价格计算] 常规风口: 面积=0.277550㎡, 计算=0.27755 × 130 × 1 = 36.081500000000005
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:59 🔧 [价格计算] 最终价格: 36.081500000000005 → 36.1
D:\personal\2025Projects\factorysystem\src\hooks\use-import-preview.ts:123 🔧 [楼层总价] 楼层"2"总价计算: 6个风口, 总价=282.40000000000003
D:\personal\2025Projects\factorysystem\src\app\(factory)\factory\orders\create-table\page.tsx:8283 🔍 预览状态检查（立即）: Object
D:\personal\2025Projects\factorysystem\src\app\(factory)\factory\orders\create-table\page.tsx:8334 🎯 OCR文字解析成功，识别到 1 个项目，预览编辑器已打开
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:163 🎯 页面重新获得焦点，检查 Token 状态
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:69 🔍 检查 Token 状态...
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:78 ✅ Token 状态正常
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:163 🎯 页面重新获得焦点，检查 Token 状态
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:69 🔍 检查 Token 状态...
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:78 ✅ Token 状态正常
D:\personal\2025Projects\factorysystem\src\app\(factory)\factory\orders\create-table\page.tsx:8291 🔍 预览状态检查（延迟）: Object
D:\personal\2025Projects\factorysystem\src\app\(factory)\factory\orders\create-table\page.tsx:8308 ⚠️ 预览状态中仍然没有项目数据！
D:\personal\2025Projects\factorysystem\src\app\(factory)\factory\orders\create-table\page.tsx:8309 🔍 原始项目数据检查: Array(1)
D:\personal\2025Projects\factorysystem\src\app\(factory)\factory\orders\create-table\page.tsx:8291 🔍 预览状态检查（延迟）: Object
D:\personal\2025Projects\factorysystem\src\app\(factory)\factory\orders\create-table\page.tsx:8308 ⚠️ 预览状态中仍然没有项目数据！
D:\personal\2025Projects\factorysystem\src\app\(factory)\factory\orders\create-table\page.tsx:8309 🔍 原始项目数据检查: Array(1)
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:163 🎯 页面重新获得焦点，检查 Token 状态
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:69 🔍 检查 Token 状态...
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:78 ✅ Token 状态正常
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:163 🎯 页面重新获得焦点，检查 Token 状态
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:69 🔍 检查 Token 状态...
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:78 ✅ Token 状态正常
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:154 👁️ 页面重新可见，检查 Token 状态
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:69 🔍 检查 Token 状态...
D:\personal\2025Projects\factorysystem\src\lib\services\token-monitor.service.ts:78 ✅ Token 状态正常
hot-reloader-client.js:197 [Fast Refresh] rebuilding
report-hmr-latency.js:14 [Fast Refresh] done in 109ms
