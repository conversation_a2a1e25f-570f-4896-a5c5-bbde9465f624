/**
 * 知识库学习效果演示
 * 展示AI识别系统如何通过学习提升识别效果
 */

import { aiKnowledgeBase } from './ai-knowledge-base'

export class KnowledgeBaseDemoService {
  
  /**
   * 演示知识库学习过程
   */
  async demonstrateLearningProcess() {
    console.log('🎯 开始演示知识库学习过程...')
    
    // 第一次识别：没有历史案例
    const firstInput = `
金色时代10栋1901号房
大厅回风口：300X1500mm
出风口：110X3620mm
餐厅回风口：300X1230㎜
    `
    
    console.log('\n📝 第一次识别（无历史案例）:')
    console.log('输入:', firstInput.trim())
    
    const firstResult = {
      projects: [{
        projectName: "金色时代",
        clientInfo: "10栋1901号房",
        floors: [{
          floorName: "19",
          rooms: [{
            roomName: "大厅",
            vents: [{
              systemType: "white_return",
              dimensions: {length: 1500, width: 300},
              notes: ""
            }]
          }]
        }]
      }],
      confidence: 0.95
    }
    
    // 保存第一个案例
    await aiKnowledgeBase.saveRecognitionCase(
      firstInput.trim(),
      firstResult,
      0.95,
      'demo_system'
    )
    console.log('✅ 第一个案例已保存到知识库')
    
    // 第二次识别：相似案例
    const secondInput = `
金色时代10栋1902号房
大厅回风口：300X1600mm
出风口：110X3700mm
餐厅回风口：300X1300㎜
    `
    
    console.log('\n📝 第二次识别（有相似案例）:')
    console.log('输入:', secondInput.trim())
    
    // 查找相似案例
    const similarCases = aiKnowledgeBase.findSimilarCases(secondInput.trim(), 3)
    console.log(`🧠 找到 ${similarCases.length} 个相似案例`)
    
    if (similarCases.length > 0) {
      console.log('📊 相似案例详情:')
      similarCases.forEach((case_, index) => {
        const similarity = this.calculateSimilarity(secondInput.trim(), case_.originalText)
        console.log(`  ${index + 1}. 相似度: ${(similarity * 100).toFixed(1)}%`)
        console.log(`     原文: ${case_.originalText.substring(0, 50)}...`)
        console.log(`     置信度: ${case_.confidence}`)
      })
    }
    
    // 生成智能提示
    const basePrompt = "分析风口订单文本..."
    const smartPrompt = aiKnowledgeBase.generateSmartPrompt(secondInput.trim(), basePrompt)
    console.log('\n🚀 生成的智能提示:')
    console.log(smartPrompt.substring(0, 200) + '...')
    
    // 第三次识别：高度相似案例（演示快速识别）
    const thirdInput = `
金色时代10栋1901号房
大厅回风口：300X1500mm
出风口：110X3620mm
    `
    
    console.log('\n📝 第三次识别（高度相似案例）:')
    console.log('输入:', thirdInput.trim())
    
    const highSimilarCases = aiKnowledgeBase.findSimilarCases(thirdInput.trim(), 1)
    if (highSimilarCases.length > 0) {
      const similarity = this.calculateSimilarity(thirdInput.trim(), highSimilarCases[0].originalText)
      console.log(`⚡ 发现高度相似案例，相似度: ${(similarity * 100).toFixed(1)}%`)
      
      if (similarity > 0.95) {
        console.log('🎯 直接使用历史案例结果，跳过AI调用！')
        console.log('📈 节省时间: ~3-5秒')
        console.log('🎯 识别准确率: 基于历史成功案例')
      }
    }
    
    // 显示知识库统计
    const stats = aiKnowledgeBase.getKnowledgeBaseStats()
    console.log('\n📊 知识库统计:')
    console.log(`  总案例数: ${stats.totalCases}`)
    console.log(`  平均置信度: ${stats.averageConfidence}`)
    console.log(`  常见项目格式: ${stats.commonPatterns.projects.slice(0, 3).join(', ')}`)
    console.log(`  常见风口类型: ${stats.commonPatterns.ventTypes.slice(0, 3).join(', ')}`)
    
    console.log('\n🎉 知识库学习演示完成！')
  }
  
  /**
   * 演示不同师傅内容的学习过程
   */
  async demonstrateDifferentMasterStyles() {
    console.log('\n🔧 演示不同师傅风格的学习...')
    
    const masterStyles = [
      {
        name: "张师傅",
        style: "简洁风格",
        input: `
天润城16-1905
大厅出风口:3090✘150分段
餐厅出风口:2615✘150
进风口:3110✘300分段
        `
      },
      {
        name: "李师傅", 
        style: "详细风格",
        input: `
项目：武宣镇天润城小区16栋1905号房
房间：大厅 - 出风口尺寸：3090mm×150mm（需要分段）
房间：餐厅 - 出风口尺寸：2615mm×150mm
房间：客厅 - 进风口尺寸：3110mm×300mm（分段加工）
        `
      },
      {
        name: "王师傅",
        style: "表格风格", 
        input: `
地址：天润城16栋1905
大厅 | 出风口 | 3090×150 | 分段
餐厅 | 出风口 | 2615×150 | 
客厅 | 回风口 | 3110×300 | 分段
        `
      }
    ]
    
    for (const master of masterStyles) {
      console.log(`\n👨‍🔧 ${master.name}（${master.style}）:`)
      console.log('输入格式:', master.input.trim())
      
      // 模拟保存案例
      const mockResult = {
        projects: [{
          projectName: "武宣镇天润城16",
          clientInfo: "1905",
          floors: [{ floorName: "19" }]
        }],
        confidence: 0.88
      }
      
      await aiKnowledgeBase.saveRecognitionCase(
        master.input.trim(),
        mockResult,
        0.88,
        master.name
      )
      
      console.log(`✅ ${master.name}的风格已学习`)
    }
    
    // 测试混合风格识别
    const mixedInput = `
天润城16-1906
大厅 | 出风口 | 3000×150mm | 加急
    `
    
    console.log('\n🔄 测试混合风格识别:')
    console.log('输入:', mixedInput.trim())
    
    const similarCases = aiKnowledgeBase.findSimilarCases(mixedInput.trim(), 3)
    console.log(`🧠 找到 ${similarCases.length} 个相似案例，来自不同师傅的风格`)
    
    if (similarCases.length > 0) {
      console.log('📚 AI将综合学习以下风格:')
      similarCases.forEach((case_, index) => {
        console.log(`  ${index + 1}. ${case_.verifiedBy || '未知师傅'}的风格`)
      })
    }
    
    console.log('\n🎯 知识库现在可以识别多种师傅的输入风格！')
  }
  
  /**
   * 计算文本相似度（简化版本）
   */
  private calculateSimilarity(text1: string, text2: string): number {
    const normalize = (str: string) => str.toLowerCase().replace(/\s+/g, '').replace(/[^\w\u4e00-\u9fff]/g, '')
    const norm1 = normalize(text1)
    const norm2 = normalize(text2)
    
    if (norm1 === norm2) return 1
    
    const longer = norm1.length > norm2.length ? norm1 : norm2
    const shorter = norm1.length > norm2.length ? norm2 : norm1
    
    if (longer.length === 0) return 1
    
    // 简单的字符匹配计算
    let matches = 0
    for (let i = 0; i < shorter.length; i++) {
      if (longer.includes(shorter[i])) {
        matches++
      }
    }
    
    return matches / longer.length
  }
  
  /**
   * 清理演示数据
   */
  clearDemoData() {
    aiKnowledgeBase.clearKnowledgeBase()
    console.log('🧹 演示数据已清理')
  }
}

export const knowledgeBaseDemo = new KnowledgeBaseDemoService()
