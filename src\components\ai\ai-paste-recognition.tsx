'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Brain, Copy, CheckCircle, AlertTriangle } from 'lucide-react'
import { getDeepSeekAPI, type AIAnalysisResult } from '@/lib/services/deepseek-api'
import { getSiliconFlowAPI } from '@/lib/services/siliconflow-api'
import { getQwenAPI } from '@/lib/services/qwen-api'
import { API_KEYS, checkApiKeyConfiguration } from '@/lib/config/api-keys'
import SiliconFlowSetupGuide from '@/components/guide/siliconflow-setup-guide'
import { ModelSwitcher } from './model-switcher'
import { complexityDetector } from '@/lib/services/complexity-detector'

interface AIRecognitionProps {
  onResult?: (result: AIAnalysisResult) => void
}

export default function AIPasteRecognition({ onResult }: AIRecognitionProps) {
  const [inputText, setInputText] = useState('')
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [result, setResult] = useState<AIAnalysisResult | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [analysisProgress, setAnalysisProgress] = useState('')
  const [selectedModel, setSelectedModel] = useState<'deepseek-chat' | 'deepseek-reasoner' | 'auto'>('auto')
  const [complexityAnalysis, setComplexityAnalysis] = useState<any>(null)
  const [apiProvider, setApiProvider] = useState<'deepseek' | 'siliconflow' | 'qwen'>('qwen')
  const [showSetupGuide, setShowSetupGuide] = useState(false)
  const [testingConnection, setTestingConnection] = useState(false)

  // 检查API密钥配置
  const apiKeyStatus = checkApiKeyConfiguration()

  const handleAnalyze = async () => {
    if (!inputText.trim()) {
      setError('请输入要分析的文本内容')
      return
    }

    setIsAnalyzing(true)
    setError(null)
    setResult(null)
    setAnalysisProgress('正在分析文本复杂度...')

    try {
      console.log('🚀 开始AI智能识别...')

      // 分析文本复杂度
      const complexity = complexityDetector.analyzeComplexity(inputText)
      setComplexityAnalysis(complexity)
      console.log('🧮 复杂度分析:', complexity)

      setAnalysisProgress('正在连接AI服务...')

      let analysisResult: AIAnalysisResult

      if (apiProvider === 'siliconflow') {
        // 使用硅基流动API进行分析
        setAnalysisProgress('正在连接硅基流动服务...')
        try {
          const siliconFlow = getSiliconFlowAPI(API_KEYS.SILICONFLOW)
          analysisResult = await siliconFlow.analyzeVentOrder(inputText)
        } catch (siliconFlowError) {
          console.warn('🔄 硅基流动API失败，自动切换到DeepSeek官方API:', siliconFlowError)
          setAnalysisProgress('硅基流动服务超时，自动切换到DeepSeek官方API...')

          // 自动回退到DeepSeek官方API
          const deepSeek = getDeepSeekAPI(API_KEYS.DEEPSEEK)
          const complexity = complexityDetector.analyzeComplexity(inputText)
          const modelToUse = complexity.recommendedModel
          analysisResult = await deepSeek.analyzeVentOrder(inputText, modelToUse)
          analysisResult.provider = 'deepseek-fallback'
          analysisResult.fallbackReason = siliconFlowError instanceof Error ? siliconFlowError.message : '硅基流动服务不可用'
        }
      } else if (apiProvider === 'qwen') {
        // 使用通义千问API进行分析
        setAnalysisProgress('正在连接通义千问服务...')
        try {
          const qwen = getQwenAPI(API_KEYS.QWEN)
          analysisResult = await qwen.analyzeVentOrder(inputText)
        } catch (qwenError) {
          console.warn('🔄 通义千问API失败，自动切换到DeepSeek官方API:', qwenError)
          setAnalysisProgress('通义千问服务超时，自动切换到DeepSeek官方API...')

          // 自动回退到DeepSeek官方API
          const deepSeek = getDeepSeekAPI(API_KEYS.DEEPSEEK)
          const complexity = complexityDetector.analyzeComplexity(inputText)
          const modelToUse = complexity.recommendedModel
          analysisResult = await deepSeek.analyzeVentOrder(inputText, modelToUse)
          analysisResult.provider = 'deepseek-fallback'
          analysisResult.fallbackReason = qwenError instanceof Error ? qwenError.message : '通义千问服务不可用'
        }
      } else {
        // 使用DeepSeek API进行分析
        const deepSeek = getDeepSeekAPI(API_KEYS.DEEPSEEK)

        // 确定使用的模型
        let modelToUse: string | undefined
        if (selectedModel === 'auto') {
          modelToUse = complexity.recommendedModel
          setAnalysisProgress(`AI推荐使用${modelToUse === 'deepseek-reasoner' ? 'R1推理模型' : 'V3快速模型'}...`)
        } else {
          modelToUse = selectedModel
          setAnalysisProgress(`使用${modelToUse === 'deepseek-reasoner' ? 'R1推理模型' : 'V3快速模型'}处理中...`)
        }

        analysisResult = await deepSeek.analyzeVentOrder(inputText, modelToUse)
      }

      console.log('✅ AI识别完成:', analysisResult)
      setResult(analysisResult)
      
      // 回调给父组件
      if (onResult) {
        onResult(analysisResult)
      }
      
    } catch (err) {
      console.error('❌ AI识别失败:', err)
      setError(err instanceof Error ? err.message : '识别失败，请重试')
    } finally {
      setIsAnalyzing(false)
      setAnalysisProgress('')
    }
  }

  const handleClearAll = () => {
    setInputText('')
    setResult(null)
    setError(null)
  }

  const testConnection = async () => {
    setTestingConnection(true)
    setError(null)
    setAnalysisProgress('')

    try {
      if (apiProvider === 'siliconflow') {
        setAnalysisProgress('测试硅基流动连接...')
        console.log('🔧 开始测试硅基流动连接...')

        const siliconFlow = getSiliconFlowAPI(API_KEYS.SILICONFLOW)

        // 使用专门的测试方法
        const testResult = await siliconFlow.testConnection()

        setAnalysisProgress('')
        console.log('🔧 硅基流动连接测试结果:', testResult)

        if (testResult.success) {
          alert(`✅ 硅基流动连接测试成功！
响应时间: ${(testResult.duration/1000).toFixed(2)}秒
状态: 可用
网络: 正常`)
        } else {
          throw new Error(testResult.error || '连接测试失败')
        }
      } else if (apiProvider === 'qwen') {
        setAnalysisProgress('测试通义千问连接...')
        console.log('🔧 开始测试通义千问连接...')

        const qwen = getQwenAPI(API_KEYS.QWEN)

        // 使用专门的测试方法
        const testResult = await qwen.testConnection()

        setAnalysisProgress('')
        console.log('🔧 通义千问连接测试结果:', testResult)

        if (testResult.success) {
          alert(`✅ 通义千问连接测试成功！
响应时间: ${(testResult.duration/1000).toFixed(2)}秒
状态: 可用
网络: 正常`)
        } else {
          throw new Error(testResult.error || '连接测试失败')
        }
      } else {
        setAnalysisProgress('测试DeepSeek连接...')
        console.log('🔧 开始测试DeepSeek连接...')

        const deepSeek = getDeepSeekAPI(API_KEYS.DEEPSEEK)

        // 简单测试
        const startTime = performance.now()
        const testResult = await deepSeek.analyzeVentOrder('测试：出风口 100×100 白色 1个', 'deepseek-chat', true)
        const duration = performance.now() - startTime

        setAnalysisProgress('')
        console.log('✅ DeepSeek连接测试成功:', testResult)

        alert(`✅ DeepSeek连接测试成功！
响应时间: ${(duration/1000).toFixed(2)}秒
识别结果: ${testResult.projects?.length || 0} 个项目
状态: 可用`)
      }
    } catch (error) {
      setAnalysisProgress('')
      console.error('❌ 连接测试失败:', error)

      const errorMessage = error instanceof Error ? error.message : '未知错误'
      setError(`连接测试失败: ${errorMessage}`)

      // 显示详细错误信息
      const providerName = apiProvider === 'siliconflow' ? '硅基流动' :
                          apiProvider === 'qwen' ? '通义千问' : 'DeepSeek'
      alert(`❌ 连接测试失败
提供商: ${providerName}
错误: ${errorMessage}

建议:
1. 检查网络连接
2. 验证API Key是否正确
3. 尝试切换到另一个提供商`)
    } finally {
      setTestingConnection(false)
    }
  }

  const copyResultToClipboard = () => {
    if (result) {
      const jsonStr = JSON.stringify(result, null, 2)
      navigator.clipboard.writeText(jsonStr)
    }
  }

  return (
    <div className="space-y-6">
      {/* 输入区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-blue-600" />
            AI智能粘贴识别
            <Badge variant="secondary">DeepSeek R1</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium mb-2 block">
              请粘贴风口订单文本内容：
            </label>
            <Textarea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="请粘贴风口订单文本，支持各种格式：
例如：
北海银滩恒大度假中心五区17型2栋
3楼前厅 500×300 象牙白 2个
3楼走廊 400×200 象牙白 1个
4楼会议室 600×300 象牙白 4个

或者：
出风口              回风口
500×300            400×200  
600×350            500×250

等各种复杂格式..."
              className="min-h-[200px] font-mono text-sm"
            />
          </div>

          <div className="flex gap-2">
            <Button
              onClick={handleAnalyze}
              disabled={isAnalyzing || !inputText.trim()}
              size="lg"
              className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-6 py-3 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
            >
              {isAnalyzing ? (
                <>
                  <Loader2 className="h-5 w-5 animate-spin" />
                  {analysisProgress || 'AI分析中...'}
                </>
              ) : (
                <>
                  <Brain className="h-5 w-5" />
                  🚀 开始AI智能识别
                </>
              )}
            </Button>

            <Button
              variant="outline"
              onClick={testConnection}
              disabled={isAnalyzing || testingConnection}
              className="flex items-center gap-2"
            >
              {testingConnection ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  测试中...
                </>
              ) : (
                <>
                  🔧 测试连接
                </>
              )}
            </Button>

            <Button
              variant="outline"
              onClick={handleClearAll}
              disabled={isAnalyzing}
            >
              清空重置
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* API提供商选择器 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            🚀 API提供商选择
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 flex-wrap">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="apiProvider"
                value="deepseek"
                checked={apiProvider === 'deepseek'}
                onChange={(e) => setApiProvider(e.target.value as 'deepseek' | 'siliconflow' | 'qwen')}
                className="rounded"
              />
              <span>DeepSeek 官方</span>
              <Badge variant="outline">原生API</Badge>
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="apiProvider"
                value="siliconflow"
                checked={apiProvider === 'siliconflow'}
                onChange={(e) => setApiProvider(e.target.value as 'deepseek' | 'siliconflow' | 'qwen')}
                className="rounded"
              />
              <span>硅基流动</span>
              <Badge variant="secondary">10x+ 速度提升</Badge>
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="apiProvider"
                value="qwen"
                checked={apiProvider === 'qwen'}
                onChange={(e) => setApiProvider(e.target.value as 'deepseek' | 'siliconflow' | 'qwen')}
                className="rounded"
              />
              <span>通义千问</span>
              <Badge variant="default">中文优化</Badge>
            </label>
          </div>
          <div className="mt-2 text-sm text-gray-600">
            {apiProvider === 'deepseek' ? (
              <div>
                <span>使用DeepSeek官方API，稳定可靠</span>
                {!apiKeyStatus.deepseek && (
                  <div className="mt-1 text-yellow-600">
                    ⚠️ 使用默认API密钥，建议配置自己的密钥
                  </div>
                )}
              </div>
            ) : apiProvider === 'siliconflow' ? (
              <div>
                <span>使用硅基流动加速服务，速度提升10倍以上，国内访问更稳定</span>
                {!apiKeyStatus.siliconflow && (
                  <div className="mt-1 text-red-600">
                    ❌ 硅基流动API密钥未配置，请先申请并配置API密钥
                    <Button
                      variant="link"
                      size="sm"
                      onClick={() => setShowSetupGuide(true)}
                      className="p-0 h-auto ml-2 text-blue-600"
                    >
                      查看申请指南
                    </Button>
                  </div>
                )}
              </div>
            ) : (
              <div>
                <span>使用阿里云通义千问大模型，中文理解能力强，响应速度快</span>
                {!apiKeyStatus.qwen && (
                  <div className="mt-1 text-red-600">
                    ❌ 通义千问API密钥未配置，请先申请并配置API密钥
                    <Button
                      variant="link"
                      size="sm"
                      onClick={() => window.open('https://help.aliyun.com/zh/dashscope/developer-reference/activate-dashscope-and-create-an-api-key', '_blank')}
                      className="p-0 h-auto ml-2 text-blue-600"
                    >
                      查看申请指南
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 模型选择器 */}
      {apiProvider === 'deepseek' && (
        <ModelSwitcher
          complexityAnalysis={complexityAnalysis}
          selectedModel={selectedModel}
          onModelSelect={setSelectedModel}
        />
      )}

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 识别结果 */}
      {result && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                AI识别结果
              </CardTitle>
              <div className="flex items-center gap-2">
                <Badge variant={result.confidence > 0.8 ? "default" : "secondary"}>
                  置信度: {(result.confidence * 100).toFixed(1)}%
                </Badge>
                {result.modelUsed && (
                  <Badge variant="outline" className={
                    result.modelUsed === 'deepseek-reasoner'
                      ? 'bg-purple-50 text-purple-700 border-purple-200'
                      : result.modelUsed === 'knowledge_base'
                      ? 'bg-amber-50 text-amber-700 border-amber-200'
                      : 'bg-green-50 text-green-700 border-green-200'
                  }>
                    {result.modelUsed === 'deepseek-reasoner'
                      ? '🧠 R1推理模型'
                      : result.modelUsed === 'knowledge_base'
                      ? '⚡ 知识库匹配'
                      : '🚀 V3快速模型'}
                  </Badge>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={copyResultToClipboard}
                >
                  <Copy className="h-4 w-4 mr-1" />
                  复制结果
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 警告信息 */}
            {result.warnings && result.warnings.length > 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-1">
                    {result.warnings.map((warning, index) => (
                      <div key={index}>• {warning}</div>
                    ))}
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* 项目信息 */}
            {result.projects.map((project, projectIndex) => (
              <div key={projectIndex} className="border rounded-lg p-4 space-y-3">
                {project.projectName && (
                  <div>
                    <span className="font-medium text-blue-600">项目名称：</span>
                    {project.projectName}
                  </div>
                )}
                
                {project.clientInfo && (
                  <div>
                    <span className="font-medium text-green-600">客户信息：</span>
                    {project.clientInfo}
                  </div>
                )}

                {/* 楼层信息 */}
                {project.floors.map((floor, floorIndex) => (
                  <div key={floorIndex} className="ml-4 border-l-2 border-gray-200 pl-4">
                    <div className="font-medium text-purple-600 mb-2">
                      {floor.floorName}
                    </div>
                    
                    {/* 房间信息 */}
                    {floor.rooms.map((room, roomIndex) => (
                      <div key={roomIndex} className="ml-4 mb-3">
                        {room.roomName && (
                          <div className="font-medium text-orange-600 mb-1">
                            {room.roomName}
                          </div>
                        )}
                        
                        {/* 风口信息 */}
                        <div className="space-y-1">
                          {room.vents.map((vent, ventIndex) => (
                            <div key={ventIndex} className="flex flex-wrap gap-2 text-sm bg-gray-50 p-2 rounded">
                              <Badge variant="outline">{vent.type}</Badge>
                              <Badge variant="secondary">
                                {vent.dimensions.length}×{vent.dimensions.width}{vent.dimensions.unit}
                              </Badge>
                              {vent.color && (
                                <Badge style={{ backgroundColor: vent.color === '象牙白' ? '#FFF8DC' : '#f3f4f6' }}>
                                  {vent.color}
                                </Badge>
                              )}
                              {vent.quantity && (
                                <Badge variant="outline">{vent.quantity}个</Badge>
                              )}
                              {vent.notes && (
                                <span className="text-gray-600">备注: {vent.notes}</span>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            ))}

            {/* R1推理过程 */}
            {result.reasoningContent && (
              <details className="mt-4">
                <summary className="cursor-pointer text-sm text-purple-600 font-medium">
                  🧠 查看AI推理过程（R1模型特有）
                </summary>
                <div className="mt-2 p-3 bg-purple-50 border border-purple-200 rounded text-sm">
                  <pre className="whitespace-pre-wrap text-purple-800 max-h-60 overflow-auto">
                    {result.reasoningContent}
                  </pre>
                </div>
              </details>
            )}

            {/* 原始响应（调试用） */}
            {result.rawResponse && (
              <details className="mt-4">
                <summary className="cursor-pointer text-sm text-gray-600">
                  查看原始AI响应（调试用）
                </summary>
                <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-40">
                  {result.rawResponse}
                </pre>
              </details>
            )}
          </CardContent>
        </Card>
      )}

      {/* 硅基流动设置指南对话框 */}
      {showSetupGuide && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">硅基流动 API 申请指南</h3>
              <Button
                variant="ghost"
                onClick={() => setShowSetupGuide(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </Button>
            </div>
            <SiliconFlowSetupGuide />
            <div className="flex justify-end mt-6">
              <Button
                onClick={() => setShowSetupGuide(false)}
                className="bg-blue-600 hover:bg-blue-700"
              >
                完成
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
