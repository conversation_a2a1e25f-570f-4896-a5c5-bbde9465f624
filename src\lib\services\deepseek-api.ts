/**
 * DeepSeek API 服务
 * 专门用于风口订单智能识别
 */

import { aiKnowledgeBase } from './ai-knowledge-base'
import { getDeepSeekConfig, type DeepSeekConfig } from '../config/deepseek.config'
import { complexityDetector, type ComplexityAnalysis } from './complexity-detector'

interface DeepSeekResponse {
  choices: Array<{
    message: {
      content: string;
      reasoning_content?: string; // R1模型的推理内容
    };
  }>;
}

interface VentInfo {
  type: string;
  dimensions: {
    length: number;
    width: number;
    unit: string;
  };
  color?: string;
  quantity?: number;
  notes?: string;
}

interface RoomInfo {
  roomName: string;
  vents: VentInfo[];
}

interface FloorInfo {
  floorName: string;
  rooms: RoomInfo[];
}

interface ProjectInfo {
  projectName?: string;
  clientInfo?: string;
  floors: FloorInfo[];
}

interface AIAnalysisResult {
  projects: ProjectInfo[];
  confidence: number;
  warnings: string[];
  rawResponse?: string;
  modelUsed?: string;
  complexityAnalysis?: ComplexityAnalysis;
  reasoningContent?: string; // R1模型的推理过程
}

class DeepSeekAPI {
  private config: DeepSeekConfig;
  private lastInputText: string = ''; // 保存输入文本供备用解析使用

  constructor(apiKey?: string) {
    if (apiKey) {
      // 兼容旧的构造方式
      this.config = {
        apiKey,
        baseURL: 'https://api.deepseek.com/v1/chat/completions',
        model: 'deepseek-chat',
        maxTokens: 1500, // 确保完整性，避免JSON截断
        temperature: 0.2, // 适中的temperature平衡速度和质量
        timeout: 30000
      };
    } else {
      // 使用环境变量配置
      this.config = getDeepSeekConfig();
    }
  }

  /**
   * 分析风口订单文本（集成知识库和智能模型选择）
   */
  async analyzeVentOrder(text: string, forceModel?: string, fastMode: boolean = true): Promise<AIAnalysisResult> {
    const startTime = performance.now();
    console.log('🚀 [DeepSeek] 开始风口订单分析...');

    // 保存输入文本供备用解析使用
    this.lastInputText = text;

    try {
      console.log('🤖 开始DeepSeek AI分析...');
      console.log('📝 输入文本长度:', text.length, '字符');

      // 🔍 分析文本复杂度
      const complexityAnalysis = complexityDetector.analyzeComplexity(text);
      console.log('🧮 复杂度分析:', complexityAnalysis);

      // 🧠 检查知识库中的相似案例
      const similarCases = aiKnowledgeBase.findSimilarCases(text, 3);
      if (similarCases.length > 0) {
        console.log(`🧠 找到 ${similarCases.length} 个相似案例，加速识别`);

        // 🔧 暂时禁用直接使用历史案例，确保每次都进行AI分析
        // 只有在用户明确确认结果正确后，才应该直接使用知识库结果
        // 当前阶段优先保证识别准确性，而不是速度
        console.log('📚 找到相似案例，但仍进行AI分析以确保准确性');

        // 注释掉直接使用历史案例的逻辑
        /*
        const highSimilarCase = similarCases.find(c =>
          aiKnowledgeBase['calculateSimilarity'](text, c.originalText) > 0.98  // 提高阈值
        );

        if (highSimilarCase) {
          console.log('⚡ 使用高度相似案例，跳过AI调用');
          return {
            ...highSimilarCase.recognizedData,
            confidence: highSimilarCase.confidence,
            warnings: ['基于相似案例快速识别'],
            modelUsed: 'knowledge_base',
            complexityAnalysis
          };
        }
        */
      }

      // 🎯 暂时强制使用V3模型（R1太慢）
      const selectedModel = forceModel || 'deepseek-chat';  // 强制使用V3
      console.log(`🎯 选择模型: ${selectedModel} (复杂度: ${complexityAnalysis.score}) - 强制使用V3提升速度`);

      // 🚀 生成智能提示（优化：统一使用快速模式提升速度）
      const promptStartTime = performance.now();
      const smartPrompt = this.buildFastPrompt(text);
      console.log(`⚡ [性能监控] Prompt生成耗时: ${(performance.now() - promptStartTime).toFixed(2)}ms`);
      console.log(`⚡ 使用快速模式进行识别（已优化）`);
      console.log(`📏 Prompt长度: ${smartPrompt.length} 字符`);

      const apiStartTime = performance.now();
      console.log('🌐 [性能监控] 开始API调用:', new Date().toISOString());
      const response = await this.callDeepSeekAPI(smartPrompt, selectedModel);
      console.log(`🌐 [性能监控] API调用耗时: ${(performance.now() - apiStartTime).toFixed(2)}ms`);

      console.log('🔍 DeepSeek原始响应长度:', response.content?.length || 0, '字符');

      const parseStartTime = performance.now();
      const result = this.parseResponse(response, selectedModel);
      console.log(`🔧 [性能监控] JSON解析耗时: ${(performance.now() - parseStartTime).toFixed(2)}ms`);
      result.modelUsed = selectedModel;
      result.complexityAnalysis = complexityAnalysis;

      const totalTime = performance.now() - startTime;
      console.log(`🎉 [性能监控] AI分析总耗时: ${totalTime.toFixed(2)}ms (${(totalTime/1000).toFixed(2)}秒)`);
      console.log('✅ AI分析完成:', result);

      // 💾 暂时禁用自动保存到知识库
      // 只有用户确认结果正确后，才应该保存到知识库
      // 避免保存错误的识别结果
      console.log('📝 AI识别完成，等待用户确认后再保存到知识库');

      /*
      // 原自动保存逻辑（暂时禁用）
      if (result.confidence > 0.9) {  // 提高保存阈值
        try {
          await aiKnowledgeBase.saveRecognitionCase(
            text,
            result,
            result.confidence,
            `${selectedModel}_system`
          );
          console.log('💾 案例已保存到知识库');
        } catch (saveError) {
          console.warn('⚠️ 保存案例到知识库失败:', saveError);
        }
      }
      */

      return result;
    } catch (error) {
      console.error('❌ DeepSeek API调用失败:', error);
      throw new Error(`AI分析失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 构建分析prompt（精简版，提升性能）
   */
  private buildAnalysisPrompt(text: string): string {
    return this.buildFastPrompt(text);
  }

  /**
   * 精简版prompt（提升性能）
   */
  private buildFastPrompt(text: string): string {
    return `你是一位资深的暖通空调工程师和数据分析专家，拥有20年的风口订单处理经验。请运用你的专业知识，深度分析以下文本内容。

🎯 核心任务：像真正的工程师一样思考，理解文本的业务逻辑，而不是简单的模式匹配。

📝 待分析文本：
${text}

🧠 智能分析要求：

**1. 文本结构理解**：
- 分析文本的组织方式和层级关系
- 识别项目信息、楼层分布、房间布局
- 理解上下文关系和业务逻辑

**2. 专业术语识别**：
- 风口类型：出风口、回风口、送风口、排风口、新风口等
- 房间类型：办公室、会议室、走廊、卫生间、机房等
- 工程术语：检修口、维修口、软管连接、普通下出等

**3. 尺寸智能解析**：
- 识别各种尺寸表达方式：×、✖️、*、x、X、乘、by等
- 智能单位推断：<100的数字可能是厘米，需转换为毫米
- 处理OCR错误：568.2可能是5682的误识别
- 理解工程惯例：长×宽的表达习惯

**4. 智能风口类型判断**：
- 宽度≤254mm = 出风口 (systemType: "double_white_outlet")
- 宽度≥255mm = 回风口 (systemType: "white_return")
- 结合关键词和上下文进行综合判断

**5. 项目结构分析**：
- 智能判断是单项目还是多项目
- 理解楼层-房间-风口的层级关系
- 识别项目地址和客户信息

返回格式（必须是有效的JSON）：
{
  "projects": [{
    "projectName": "项目名称",
    "clientInfo": "客户信息",
    "floors": [{
      "floorName": "一楼",
      "rooms": [{
        "roomName": "房间名称",
        "vents": [
          {
            "systemType": "double_white_outlet",
            "originalType": "出风口",
            "dimensions": {"length": 2665, "width": 155, "unit": "mm"},
            "color": "white",
            "quantity": 1,
            "notes": "备注信息"
          }
        ]
      }]
    }]
  }]
}

🎯 请运用专业知识进行智能分析，展现真正的AI理解能力！返回完整有效的JSON，不要添加解释文字。`;
  }

  /**
   * 详细版prompt（保留原有功能）
   */
  private buildDetailedPrompt(text: string): string {
    return `你是专业的风口订单识别专家。请仔细分析文本，识别出每一个风口。

⚠️ 重要要求：
1. 必须识别文本中的每一个风口，包括重复的尺寸
2. 相同尺寸出现多次时，每次都要单独识别
3. 必须进行智能单位转换：小于100的数字推断为厘米，需转换为毫米
4. 处理异常小数点：如568.2×14.5可能是5682×145的识别错误

文本：
${text}

🔍 风口类型识别规则（严格按照以下规则判断）：

📤 出风口识别：
- 关键词：出风口、出风、送风口、送风、客出风、主出风、餐出风、卧出风
- 特征：通常宽度较小（100-250mm），用于送风
- 默认类型：双层白色出风口（double_white_outlet）

📥 回风口识别：
- 关键词：回风口、回风、进气口、进气、客回风、主回风、餐回风、卧回风
- 特征：通常宽度较大（≥255mm），用于回风
- 默认类型：白色回风口（white_return）

📏 线型风口识别：
- 关键词：线型、线形、线条、条形、条型
- 特征：细长条状，专门的线型风口
- 默认类型：白色线型风口（white_linear）

🔧 检修口识别：
- 关键词：检修口、检修、维修口、维修
- 特征：用于设备检修
- 类型：检修口（maintenance）

🎨 颜色识别：
- 黑色系：包含"黑"、"黑色"关键词
- 白色系：默认或包含"白"、"白色"关键词

⚠️ 重要：如果文本中没有明确的"回风"关键词，不要默认识别为回风口！
- 只有明确包含"回风"、"回风口"等关键词时才识别为回风口
- 没有明确类型标识时，根据尺寸判断：宽度<255mm识别为出风口，≥255mm识别为回风口

楼层和房间处理规则（保守原则）：
1. 楼层识别优先级（严格限制）：
   - ✅ 明确楼层标识（如"19楼"、"二楼"）独立行
   - ❌ **严禁推断**：不要从房号提取楼层（A201≠2楼，1901≠19楼）
   - ✅ 默认处理：无明确楼层时统一使用"1楼"
2. 楼层格式：输出"1楼"、"2楼"格式
3. 房间信息：大厅、餐厅、主卧、次卧、小房间等作为房间名称
4. 客户信息：完整的项目地址和房号信息
5. 备注字段处理：
   - 只保留有意义的技术规格和特殊要求（如"分段"、"单边"、"加急"等）
   - 不要生成"未指定房间"、"未识别房间"等无意义的备注
   - 如果没有特殊备注，notes字段留空或不填写

系统风口类型代码：
- double_white_outlet: 双层白色出风口
- double_black_outlet: 双层黑色出风口
- white_return: 白色回风口
- black_return: 黑色回风口
- white_linear: 白色线型风口
- black_linear: 黑色线型风口
- white_linear_return: 白色线型回风口
- black_linear_return: 黑色线型回风口
- maintenance: 检修口

尺寸和备注解析规则：
1. 尺寸格式识别：支持×、X、✘、*、x等分隔符
2. 🔧 智能单位识别和转换（重要）：
   - 如果数字<100，推断为厘米，转换为毫米：141*30 → 1410×300mm
   - 如果数字≥100，推断为毫米：568*145 → 568×145mm
   - 小数点尺寸修正：568.2×14.5 → 5682×145mm（去除异常小数点）
   - 所有尺寸最终统一为毫米单位
3. 🔧 智能尺寸排序：确保大值作为长度，小值作为宽度
4. 🔧 重复尺寸处理：
   - 文本中出现"141*30     141*30"时，必须识别为两个独立的风口
   - 每个尺寸都要单独处理，不能合并或忽略
5. 备注提取：只提取有意义的技术规格和特殊要求
   - "3090✘150分段" → 尺寸3090×150，备注"分段"
   - "93.7*30（单边）" → 尺寸937×300，备注"单边"
   - 不要生成"未指定房间"、"未识别房间"等无意义备注
6. 数量识别：如果有数字+个/只等单位，提取为数量
7. 特殊备注：分段、加急、定制、帮减、单边等工艺要求
8. 备注原则：
   - 只保留技术规格、工艺要求、特殊说明
   - 没有特殊备注时，notes字段留空
   - 不要填写房间相关的占位符文本

⚠️ 关键提醒：
- 风口尺寸通常在100-5000mm范围内
- 如果识别出异常小的数值（如14.5mm宽度），很可能是单位识别错误
- 必须对每个尺寸进行单位转换和合理性检查
- 重复的尺寸必须分别识别，不能遗漏
- 备注字段只填写有意义的内容，不要填写"未指定房间"等占位符

🎯 识别示例：

示例1 - 明确类型标识：
输入："客厅回风141*30     141*30      客出风568.2*14.5"
正确处理：
1. "客厅回风141*30" → systemType: "white_return", originalType: "客厅回风", 长度1410mm，宽度300mm
2. "141*30" → systemType: "white_return", originalType: "回风", 长度1410mm，宽度300mm（继承前文类型）
3. "客出风568.2*14.5" → systemType: "double_white_outlet", originalType: "客出风", 长度5682mm，宽度145mm

示例2 - 无明确类型（根据尺寸判断）：
输入："2665×155  1525×255  3805×155"
正确处理：
1. "2665×155" → systemType: "double_white_outlet", originalType: "出风口", 长度2665mm，宽度155mm（宽度<255mm，判断为出风口）
2. "1525×255" → systemType: "white_return", originalType: "回风口", 长度1525mm，宽度255mm（宽度≥255mm，判断为回风口）
3. "3805×155" → systemType: "double_white_outlet", originalType: "出风口", 长度3805mm，宽度155mm（宽度<255mm，判断为出风口）

📋 返回JSON格式：
{
  "projects": [{
    "projectName": "项目名称",
    "clientInfo": "客户信息",
    "floors": [{
      "floorName": "楼层",
      "rooms": [{
        "roomName": "房间",
        "vents": [{
          "systemType": "系统风口类型代码（必须是下面列表中的一个）",
          "originalType": "原始风口描述（如：客厅回风、客出风、出风口、回风口等）",
          "dimensions": {"length": 数值, "width": 数值, "unit": "mm"},
          "color": "white|black",
          "quantity": 数量,
          "notes": "备注（仅包含技术规格和特殊要求，无特殊备注时留空）"
        }]
      }]
    }]
  }],
  "confidence": 0.95,
  "warnings": ["问题提醒"]
}

🔧 systemType必须从以下列表中选择：
- double_white_outlet: 双层白色出风口（默认出风口类型）
- double_black_outlet: 双层黑色出风口
- white_return: 白色回风口（默认回风口类型）
- black_return: 黑色回风口
- white_linear: 白色线型风口
- black_linear: 黑色线型风口
- white_linear_return: 白色线型回风口
- black_linear_return: 黑色线型回风口
- maintenance: 检修口

🔧 originalType填写规则：
- 如果文本中有明确描述（如"客厅回风"），直接使用
- 如果只有尺寸没有描述，根据判断结果填写："出风口"或"回风口"
- 不要留空，必须有描述性文字

⚠️ 最重要的提醒：
1. 🎯 不要默认所有风口都是回风口！必须根据关键词和尺寸准确判断
2. � 有"出风"、"送风"关键词或宽度<255mm时，识别为出风口
3. 📥 有"回风"、"进气"关键词或宽度≥255mm时，识别为回风口
4. 📝 originalType字段不能为空，必须填写描述性文字
5. 🔧 systemType必须从上述列表中准确选择，不能随意填写

🔧 尺寸处理要求：
- 检查是否<100（厘米单位，需×10转换为毫米）
- 去除异常小数点
- 确保大值作为长度，小值作为宽度
- 重复尺寸分别识别，不能合并

现在开始分析文本，严格按照上述规则识别每个风口的类型！`;
  }

  /**
   * 调用DeepSeek API
   */
  private async callDeepSeekAPI(prompt: string, model?: string): Promise<{content: string, reasoningContent?: string}> {
    const selectedModel = model || this.config.model;

    // R1模型不支持temperature等参数
    const isReasonerModel = selectedModel === 'deepseek-reasoner';

    const requestBody: any = {
      model: selectedModel,
      messages: [
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: isReasonerModel ? 32000 : this.config.maxTokens, // 使用配置的Token数量
      stream: false
    };

    // 只有非R1模型才添加temperature等参数
    if (!isReasonerModel) {
      requestBody.temperature = this.config.temperature;
    }

    console.log('📡 [API监控] 发送API请求到:', this.config.baseURL);
    console.log('📡 [API监控] 使用模型:', selectedModel);
    console.log('📡 [API监控] 是否为推理模型:', isReasonerModel);
    console.log('📡 [API监控] 请求体大小:', JSON.stringify(requestBody).length, '字符');
    console.log('📡 [API监控] Max Tokens:', requestBody.max_tokens);

    // 添加超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

    const fetchStartTime = performance.now();
    let response: Response;

    try {
      response = await fetch(this.config.baseURL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      const fetchTime = performance.now() - fetchStartTime;
      console.log(`🌐 [API监控] Fetch请求耗时: ${fetchTime.toFixed(2)}ms`);
      clearTimeout(timeoutId);
    } catch (error: any) {
      const fetchTime = performance.now() - fetchStartTime;
      console.log(`❌ [API监控] Fetch请求失败，耗时: ${fetchTime.toFixed(2)}ms`);
      clearTimeout(timeoutId);
      if (error.name === 'AbortError') {
        throw new Error('API请求超时（30秒），请重试');
      }
      throw error;
    }

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ [API监控] API请求失败:', response.status, errorText);
      throw new Error(`API请求失败: ${response.status} ${errorText}`);
    }

    const jsonParseStartTime = performance.now();
    const data: DeepSeekResponse = await response.json();
    const jsonParseTime = performance.now() - jsonParseStartTime;
    console.log(`📊 [API监控] JSON解析耗时: ${jsonParseTime.toFixed(2)}ms`);
    console.log(`📊 [API监控] 响应数据大小: ${JSON.stringify(data).length} 字符`);

    if (!data.choices || data.choices.length === 0) {
      throw new Error('API返回数据格式错误');
    }

    const message = data.choices[0].message;
    return {
      content: message.content,
      reasoningContent: message.reasoning_content
    };
  }

  /**
   * 解析API响应
   */
  private parseResponse(response: {content: string, reasoningContent?: string}, _model?: string): AIAnalysisResult {
    try {
      const content = response.content;
      const reasoningContent = response.reasoningContent;

      console.log('🔍 原始响应内容:', content);

      // 多种方式尝试提取JSON
      let jsonStr = '';

      // 方法1: 寻找完整的JSON对象
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        jsonStr = jsonMatch[0];
      } else {
        // 方法2: 寻找```json代码块
        const codeBlockMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
        if (codeBlockMatch) {
          jsonStr = codeBlockMatch[1];
        } else {
          // 方法3: 寻找```代码块
          const generalCodeMatch = content.match(/```\s*([\s\S]*?)\s*```/);
          if (generalCodeMatch) {
            jsonStr = generalCodeMatch[1];
          } else {
            throw new Error('响应中未找到JSON格式数据');
          }
        }
      }

      console.log('🔍 提取的JSON字符串:', jsonStr);

      if (reasoningContent) {
        console.log('🧠 推理过程:', reasoningContent.substring(0, 200) + '...');
      }

      // 清理JSON字符串
      jsonStr = this.cleanJsonString(jsonStr);
      console.log('🧹 清理后的JSON长度:', jsonStr.length);
      console.log('🧹 清理后的JSON开头:', jsonStr.substring(0, 200));
      console.log('🧹 清理后的JSON结尾:', jsonStr.substring(Math.max(0, jsonStr.length - 200)));

      let parsed: any;
      try {
        parsed = JSON.parse(jsonStr);
      } catch (parseError) {
        console.error('❌ JSON解析失败，尝试修复...', parseError);

        // 第一次尝试：修复常见的JSON错误
        try {
          const fixedJson = this.fixCommonJsonErrors(jsonStr);
          console.log('🔧 修复后的JSON:', fixedJson.substring(0, 500) + '...');
          parsed = JSON.parse(fixedJson);
          console.log('✅ JSON修复成功');
        } catch (secondError) {
          console.error('❌ 第一次修复失败，尝试更激进的修复...', secondError);

          // 第二次尝试：更激进的修复
          try {
            const aggressiveFixed = this.aggressiveJsonFix(jsonStr);
            console.log('🔧 激进修复后的JSON:', aggressiveFixed.substring(0, 500) + '...');
            parsed = JSON.parse(aggressiveFixed);
            console.log('✅ 激进修复成功');
          } catch (thirdError) {
            console.error('❌ 所有修复尝试都失败，使用备用解析...', thirdError);
            // 最后尝试：从原始内容中提取信息
            throw new Error(`JSON解析完全失败: ${thirdError.message}`);
          }
        }
      }

      // 验证数据结构
      if (!parsed.projects || !Array.isArray(parsed.projects)) {
        throw new Error('响应数据结构不正确');
      }

      // 🔧 合并尺寸修正和风口类型验证（优化性能）
      const processedProjects = this.processProjectsOptimized(parsed.projects);

      return {
        projects: processedProjects,
        confidence: parsed.confidence || 0.8,
        warnings: parsed.warnings || [],
        rawResponse: content,
        reasoningContent: reasoningContent
      };
    } catch (error) {
      // 先尝试备用解析方法
      const fallbackResult = this.fallbackParse(response.content);
      if (fallbackResult) {
        console.log('✅ [DeepSeek] 备用解析成功，跳过JSON错误显示');
        return fallbackResult;
      }

      // 只有备用解析也失败时才显示错误
      console.error('❌ [DeepSeek] JSON解析失败:', error);
      console.error('❌ [DeepSeek] 原始响应:', response.content);
      console.error('❌ [DeepSeek] 备用解析也失败');

      // 解析失败时，尝试创建基础结构
      console.log('🔧 尝试创建基础结构...');
      return this.createFallbackResult(response.content, error instanceof Error ? error.message : '未知错误');
    }
  }

  /**
   * 优化的项目处理：合并尺寸修正和风口类型验证
   */
  private processProjectsOptimized(projects: any[]): any[] {
    console.log('🚀 开始优化处理项目数据...');

    return projects.map(project => ({
      ...project,
      floors: project.floors?.map((floor: any) => ({
        ...floor,
        rooms: floor.rooms?.map((room: any) => ({
          ...room,
          vents: room.vents?.map((vent: any) => {
            // 合并尺寸修正和类型验证
            const correctedDimensions = this.correctVentDimensionsOptimized(
              vent.dimensions?.length || 0,
              vent.dimensions?.width || 0
            );
            const width = correctedDimensions.width;

            // 强制验证风口类型
            let systemType = vent.systemType;
            let originalType = vent.originalType;

            if (width >= 255 && systemType === 'double_white_outlet') {
              console.log(`🔧 修正风口类型: 宽度${width}mm≥255mm，从出风口改为回风口`);
              systemType = 'white_return';
              originalType = '回风口';
            } else if (width < 255 && systemType === 'white_return') {
              console.log(`🔧 修正风口类型: 宽度${width}mm<255mm，从回风口改为出风口`);
              systemType = 'double_white_outlet';
              originalType = '出风口';
            }

            return {
              ...vent,
              dimensions: correctedDimensions,
              systemType,
              originalType
            };
          }) || []
        })) || []
      })) || []
    }));
  }

  /**
   * 优化的单个风口尺寸修正
   */
  private correctVentDimensionsOptimized(length: number, width: number): { length: number, width: number } {
    let correctedLength = length;
    let correctedWidth = width;

    // 快速单位转换
    if (correctedLength < 100) correctedLength *= 10;
    if (correctedWidth < 100) correctedWidth *= 10;

    // 确保大值作为长度，小值作为宽度
    if (correctedLength < correctedWidth) {
      [correctedLength, correctedWidth] = [correctedWidth, correctedLength];
    }

    return {
      length: correctedLength,
      width: correctedWidth
    };
  }

  /**
   * 智能尺寸修正：参考智能粘贴的逻辑
   */
  private correctDimensions(projects: any[]): any[] {
    return projects.map(project => ({
      ...project,
      floors: project.floors?.map((floor: any) => ({
        ...floor,
        rooms: floor.rooms?.map((room: any) => ({
          ...room,
          vents: room.vents?.map((vent: any) => {
            if (vent.dimensions) {
              const corrected = this.smartDimensionCorrection(
                vent.dimensions.length || 0,
                vent.dimensions.width || 0
              );

              console.log(`🔧 尺寸修正: ${vent.dimensions.length}×${vent.dimensions.width} → ${corrected.length}×${corrected.width}`);

              return {
                ...vent,
                dimensions: {
                  ...vent.dimensions,
                  length: corrected.length,
                  width: corrected.width,
                  unit: 'mm'
                }
              };
            }
            return vent;
          }) || []
        })) || []
      })) || []
    }));
  }

  /**
   * 智能尺寸修正算法
   */
  private smartDimensionCorrection(length: number, width: number): { length: number, width: number } {
    let correctedLength = length;
    let correctedWidth = width;

    console.log(`🔧 开始尺寸修正: 原始=${length}×${width}`);

    // 🔧 处理小数点异常（如568.2×14.5 → 5682×145）
    if (length % 1 !== 0 || width % 1 !== 0) {
      console.log(`🔧 检测到小数点尺寸，尝试修正...`);

      // 如果有小数点，可能是OCR识别错误，尝试去除小数点
      const lengthStr = length.toString().replace('.', '');
      const widthStr = width.toString().replace('.', '');

      const newLength = parseInt(lengthStr);
      const newWidth = parseInt(widthStr);

      console.log(`🔧 去除小数点: ${length} → ${newLength}, ${width} → ${newWidth}`);

      // 验证修正后的尺寸是否合理
      if (newLength >= 100 && newLength <= 10000 && newWidth >= 10 && newWidth <= 3000) {
        correctedLength = newLength;
        correctedWidth = newWidth;
        console.log(`✅ 小数点修正成功: ${length}×${width} → ${correctedLength}×${correctedWidth}`);
      } else {
        console.log(`❌ 小数点修正后尺寸不合理，保持原值`);
      }
    }

    // 🔧 单位智能识别和转换（关键修复）
    if (correctedLength < 100 || correctedWidth < 100) {
      console.log(`🔧 检测到小于100的尺寸，推断为厘米单位`);

      if (correctedLength < 100) {
        const oldLength = correctedLength;
        correctedLength = correctedLength * 10;
        console.log(`🔧 长度单位转换: ${oldLength}cm → ${correctedLength}mm`);
      }
      if (correctedWidth < 100) {
        const oldWidth = correctedWidth;
        correctedWidth = correctedWidth * 10;
        console.log(`🔧 宽度单位转换: ${oldWidth}cm → ${correctedWidth}mm`);
      }
    }

    // 🔧 智能排序：确保大值作为长度，小值作为宽度
    if (correctedWidth > correctedLength) {
      const temp = correctedLength;
      correctedLength = correctedWidth;
      correctedWidth = temp;
      console.log(`🔧 尺寸排序: 交换长宽 → ${correctedLength}×${correctedWidth}`);
    }

    // 🔧 合理性验证
    if (correctedLength < 50 || correctedLength > 10000) {
      console.log(`⚠️ 长度异常: ${correctedLength}mm，恢复原值`);
      correctedLength = length;
    }
    if (correctedWidth < 10 || correctedWidth > 3000) {
      console.log(`⚠️ 宽度异常: ${correctedWidth}mm，恢复原值`);
      correctedWidth = width;
    }

    console.log(`✅ 尺寸修正完成: ${length}×${width} → ${correctedLength}×${correctedWidth}`);
    return { length: correctedLength, width: correctedWidth };
  }

  /**
   * 验证和修正风口类型
   */
  private validateVentTypes(projects: any[]): any[] {
    console.log('🔧 开始验证风口类型...');

    return projects.map(project => ({
      ...project,
      floors: project.floors.map((floor: any) => ({
        ...floor,
        rooms: floor.rooms.map((room: any) => ({
          ...room,
          vents: room.vents.map((vent: any) => {
            const width = vent.dimensions?.width || 0;
            const originalSystemType = vent.systemType;

            // 🔧 强制验证：宽度≥255mm必须是回风口
            if (width >= 255 && vent.systemType === 'double_white_outlet') {
              console.log(`🔧 修正风口类型: 宽度${width}mm≥255mm，从出风口改为回风口`);
              return {
                ...vent,
                systemType: 'white_return',
                originalType: '回风口'
              };
            }

            // 🔧 验证：宽度<255mm应该是出风口
            if (width < 255 && vent.systemType === 'white_return') {
              console.log(`🔧 修正风口类型: 宽度${width}mm<255mm，从回风口改为出风口`);
              return {
                ...vent,
                systemType: 'double_white_outlet',
                originalType: '出风口'
              };
            }

            // 类型正确，无需修改
            if (originalSystemType !== vent.systemType) {
              console.log(`✅ 风口类型验证通过: 宽度${width}mm，类型${vent.systemType}`);
            }

            return vent;
          })
        }))
      }))
    }));
  }

  /**
   * 清理JSON字符串
   */
  private cleanJsonString(jsonStr: string): string {
    let cleaned = jsonStr.trim();

    // 移除markdown代码块标记
    cleaned = cleaned.replace(/^```json\s*/i, '');
    cleaned = cleaned.replace(/\s*```\s*$/, '');

    // 查找第一个 { 和最后一个 }
    const firstBrace = cleaned.indexOf('{');
    const lastBrace = cleaned.lastIndexOf('}');

    if (firstBrace !== -1 && lastBrace !== -1 && firstBrace < lastBrace) {
      cleaned = cleaned.substring(firstBrace, lastBrace + 1);
    } else {
      // 如果找不到完整的大括号，尝试找到JSON的开始
      const jsonStart = cleaned.search(/\s*{/);
      if (jsonStart !== -1) {
        cleaned = cleaned.substring(jsonStart);
      }
    }

    return cleaned
      // 移除注释
      .replace(/\/\*[\s\S]*?\*\//g, '')
      .replace(/\/\/.*$/gm, '')
      // 标准化引号
      .replace(/'/g, '"')
      // 移除多余的逗号
      .replace(/,(\s*[}\]])/g, '$1')
      // 移除控制字符
      .replace(/[\x00-\x1F\x7F]/g, '')
      // 移除多余的空白
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * 修复常见的JSON错误
   */
  private fixCommonJsonErrors(jsonStr: string): string {
    let fixed = jsonStr;

    try {
      console.log('🔧 开始修复JSON，原始长度:', jsonStr.length);

      // 0. 检查并修复截断的JSON
      fixed = this.fixTruncatedJson(fixed);

      // 1. 修复缺失的引号
      fixed = fixed.replace(/(\w+):/g, '"$1":');

      // 2. 修复尾随逗号
      fixed = fixed.replace(/,(\s*[}\]])/g, '$1');

      // 3. 修复缺失的逗号 - 改进版本
      fixed = fixed.replace(/}(\s*){/g, '},$1{');
      fixed = fixed.replace(/}(\s*)"/g, '},$1"');
      fixed = fixed.replace(/](\s*)"/g, '],$1"');
      fixed = fixed.replace(/](\s*){/g, '],$1{');

      // 4. 修复数字后缺失逗号
      fixed = fixed.replace(/(\d)(\s*)"(\w+)":/g, '$1,$2"$3":');
      fixed = fixed.replace(/(\d)(\s*){/g, '$1,$2{');

      // 5. 修复字符串后缺失逗号
      fixed = fixed.replace(/"(\s*)"(\w+)":/g, '"$1,"$2":');
      fixed = fixed.replace(/"(\s*){/g, '"$1,{');

      // 6. 修复对象/数组后缺失逗号
      fixed = fixed.replace(/}(\s*){/g, '},$1{');
      fixed = fixed.replace(/](\s*)\[/g, '],$1[');

      console.log('🔧 JSON修复完成，修复后长度:', fixed.length);
      return fixed;
    } catch (error) {
      console.error('❌ JSON修复失败:', error);
      return jsonStr; // 返回原始字符串
    }
  }

  /**
   * 修复截断的JSON
   */
  private fixTruncatedJson(jsonStr: string): string {
    let fixed = jsonStr.trim();

    // 检查是否以不完整的结构结尾
    if (fixed.endsWith('"')) {
      // 如果以引号结尾但没有闭合，可能是截断的字符串
      const lastQuoteIndex = fixed.lastIndexOf('"', fixed.length - 2);
      if (lastQuoteIndex !== -1) {
        // 检查是否是属性名
        const beforeQuote = fixed.substring(0, lastQuoteIndex).trim();
        if (beforeQuote.endsWith(':') || beforeQuote.endsWith(',')) {
          // 这是一个不完整的属性，移除它
          fixed = fixed.substring(0, lastQuoteIndex);
        }
      }
    }

    // 检查是否以不完整的对象开始结尾
    if (fixed.endsWith('": {') || fixed.endsWith('": [')) {
      // 移除不完整的属性开始
      const colonIndex = fixed.lastIndexOf('":');
      if (colonIndex !== -1) {
        const quoteIndex = fixed.lastIndexOf('"', colonIndex - 1);
        if (quoteIndex !== -1) {
          fixed = fixed.substring(0, quoteIndex);
        }
      }
    }

    // 确保JSON结构完整闭合
    let openBraces = 0;
    let openBrackets = 0;
    let inString = false;
    let escapeNext = false;

    for (let i = 0; i < fixed.length; i++) {
      const char = fixed[i];

      if (escapeNext) {
        escapeNext = false;
        continue;
      }

      if (char === '\\') {
        escapeNext = true;
        continue;
      }

      if (char === '"') {
        inString = !inString;
        continue;
      }

      if (!inString) {
        if (char === '{') openBraces++;
        else if (char === '}') openBraces--;
        else if (char === '[') openBrackets++;
        else if (char === ']') openBrackets--;
      }
    }

    // 添加缺失的闭合符号
    while (openBrackets > 0) {
      fixed += ']';
      openBrackets--;
    }
    while (openBraces > 0) {
      fixed += '}';
      openBraces--;
    }

    console.log('🔧 截断修复完成，添加了', (jsonStr.length - fixed.length), '个字符');
    return fixed;
  }

  /**
   * 激进的JSON修复方法
   */
  private aggressiveJsonFix(jsonStr: string): string {
    let fixed = jsonStr.trim();

    console.log('🚨 开始激进JSON修复');

    // 1. 尝试从截断的JSON中恢复项目信息
    let projectName = "";
    let clientInfo = "";

    // 提取项目名称
    const projectNameMatch = fixed.match(/"projectName":\s*"([^"]*)"/) ||
                            fixed.match(/项目[：:]\s*([^\n,，]+)/);
    if (projectNameMatch) {
      projectName = projectNameMatch[1].trim();
      console.log('🏢 提取到项目名称:', projectName);
    }

    // 提取客户信息
    const clientMatch = fixed.match(/"clientInfo":\s*"([^"]*)"/) ||
                       fixed.match(/客户[：:]\s*([^\n,，]+)/);
    if (clientMatch) {
      clientInfo = clientMatch[1].trim();
      console.log('� 提取到客户信息:', clientInfo);
    }

    // 2. 尝试提取所有风口对象（包括不完整的）
    const ventObjects = [];

    // 查找所有systemType定义
    const systemTypeMatches = fixed.match(/"systemType":\s*"([^"]*)"[^}]*}/g) || [];
    console.log('🔍 找到', systemTypeMatches.length, '个systemType片段');

    for (const match of systemTypeMatches) {
      try {
        // 尝试构建完整的风口对象
        const systemTypeMatch = match.match(/"systemType":\s*"([^"]*)"/);
        const lengthMatch = match.match(/"length":\s*(\d+)/);
        const widthMatch = match.match(/"width":\s*(\d+)/);
        const colorMatch = match.match(/"color":\s*"([^"]*)"/);
        const quantityMatch = match.match(/"quantity":\s*(\d+)/);

        if (systemTypeMatch && lengthMatch && widthMatch) {
          const ventObj = {
            systemType: systemTypeMatch[1],
            originalType: systemTypeMatch[1].includes('return') ? '回风口' : '出风口',
            dimensions: {
              length: parseInt(lengthMatch[1]),
              width: parseInt(widthMatch[1]),
              unit: 'mm'
            },
            color: colorMatch ? colorMatch[1] : 'white',
            quantity: quantityMatch ? parseInt(quantityMatch[1]) : 1,
            notes: ''
          };

          ventObjects.push(ventObj);
          console.log('✅ 恢复风口:', ventObj.dimensions.length + '×' + ventObj.dimensions.width);
        }
      } catch (e) {
        console.log('⚠️ 解析风口片段失败:', e.message);
      }
    }

    if (ventObjects.length > 0) {
      const reconstructed = {
        projects: [{
          projectName: projectName,
          clientInfo: clientInfo,
          floors: [{
            floorName: "一楼",
            rooms: [{
              roomName: "房间",
              vents: ventObjects
            }]
          }]
        }]
      };

      console.log('🔧 重构了', ventObjects.length, '个风口对象');
      return JSON.stringify(reconstructed);
    }

    // 2. 如果上面的方法失败，尝试从原始文本中提取尺寸信息
    console.log('🔍 尝试从文本中提取尺寸信息');

    // 改进的正则表达式：支持小数点和各种分隔符
    const dimensionRegex = /(\d+(?:\.\d+)?)\s*[×xX*✘]\s*(\d+(?:\.\d+)?)/g;
    const dimensions = fixed.match(dimensionRegex) || [];
    console.log('🔍 找到尺寸匹配:', dimensions);

    const vents = dimensions.map((dim: string, index: number) => {
      const match = dim.match(/(\d+(?:\.\d+)?)\s*[×xX*✘]\s*(\d+(?:\.\d+)?)/);
      if (match) {
        const [, lengthStr, widthStr] = match;
        let l = parseFloat(lengthStr);
        let w = parseFloat(widthStr);

        console.log(`🔧 处理尺寸 ${index + 1}: ${lengthStr}×${widthStr}`);

        // 处理小数点异常（如568.2×14.5 → 5682×145）
        if (l % 1 !== 0 || w % 1 !== 0) {
          console.log(`🔧 检测到小数点尺寸，尝试修正...`);

          // 如果有小数点，可能是OCR识别错误，尝试去除小数点
          const lengthFixed = lengthStr.replace('.', '');
          const widthFixed = widthStr.replace('.', '');

          // 如果去除小数点后的数字合理，则使用修正后的值
          const lFixed = parseInt(lengthFixed);
          const wFixed = parseInt(widthFixed);

          if (lFixed > 50 && lFixed < 10000 && wFixed > 10 && wFixed < 1000) {
            l = lFixed;
            w = wFixed;
            console.log(`✅ 小数点修正: ${lengthStr}×${widthStr} → ${l}×${w}`);
          } else {
            // 否则直接取整
            l = Math.round(l);
            w = Math.round(w);
            console.log(`⚠️ 小数点取整: ${lengthStr}×${widthStr} → ${l}×${w}`);
          }
        }

        // 智能单位转换
        const correctedL = l < 100 ? l * 10 : l;
        const correctedW = w < 100 ? w * 10 : w;

        console.log(`🔄 单位转换: ${l}×${w} → ${correctedL}×${correctedW}mm`);

        // 确保大值作为长度
        const finalLength = Math.max(correctedL, correctedW);
        const finalWidth = Math.min(correctedL, correctedW);

        return {
          systemType: finalWidth < 255 ? 'double_white_outlet' : 'white_return',
          originalType: finalWidth < 255 ? '出风口' : '回风口',
          dimensions: {
            length: finalLength,
            width: finalWidth,
            unit: 'mm'
          },
          color: 'white',
          quantity: 1,
          notes: ''
        };
      }
      return null;
    }).filter(Boolean);

    if (vents.length > 0) {
      const fallbackResult = {
        projects: [{
          projectName: "",
          clientInfo: "",
          floors: [{
            floorName: "一楼",
            rooms: [{
              roomName: "房间",
              vents: vents
            }]
          }]
        }]
      };

      console.log('🔧 从文本提取了', vents.length, '个风口');
      return JSON.stringify(fallbackResult);
    }

    // 3. 最后的备用方案：返回空结构
    console.log('🆘 返回空结构');
    return JSON.stringify({
      projects: [{
        projectName: "",
        clientInfo: "",
        floors: [{
          floorName: "一楼",
          rooms: [{
            roomName: "房间",
            vents: []
          }]
        }]
      }]
    });
  }

  /**
   * 备用解析方法 - 使用正则表达式提取关键信息（复制通义千问的逻辑）
   */
  private fallbackParse(content: string): AIAnalysisResult | null {
    try {
      console.log('🔄 [DeepSeek] 尝试备用解析方法...');

      // 从原始输入文本中解析，而不是从损坏的JSON中解析
      const inputText = this.lastInputText || content;
      const lines = inputText.split('\n').filter(line => line.trim());

      const floors: any[] = [];
      let currentFloor = '1';
      let currentRoom = '房间';
      let projectName = '未指定项目';

      // 提取项目名称（使用通义千问的逻辑）
      if (lines.length > 0) {
        const firstLine = lines[0].trim();
        if (firstLine && !this.hasVentPattern(firstLine) && this.isValidProjectName(firstLine)) {
          projectName = firstLine.replace(/^[；;]/, ''); // 移除开头的分号
        }
      }

      console.log(`🔄 [DeepSeek] 开始解析 ${lines.length} 行数据`);

      for (const line of lines) {
        const trimmedLine = line.trim();
        if (!trimmedLine) continue;

        console.log(`🔄 [DeepSeek] 处理行: "${trimmedLine}"`);

        // 识别楼层（只识别明确的楼层标识）
        const floorMatch = trimmedLine.match(/^([一二三四五六七八九十]楼|[1-9]\d*楼)$/);
        if (floorMatch) {
          const floorText = floorMatch[1];
          if (floorText.includes('一')) currentFloor = '1';
          else if (floorText.includes('二')) currentFloor = '2';
          else if (floorText.includes('三')) currentFloor = '3';
          else currentFloor = floorText.replace(/[楼F]/g, '');
          console.log(`🔄 [DeepSeek] 识别楼层: "${trimmedLine}" → "${currentFloor}"`);
          continue;
        }

        // 识别房间
        if (this.isRoomName(trimmedLine)) {
          currentRoom = trimmedLine;
          console.log(`🔄 [DeepSeek] 识别房间: "${trimmedLine}"`);
          continue;
        }

        // 识别风口
        if (this.hasVentPattern(trimmedLine)) {
          console.log(`🔄 [DeepSeek] 识别风口: "${trimmedLine}" (楼层: ${currentFloor}, 房间: ${currentRoom})`);
          this.addVentToFloors(floors, currentFloor, currentRoom, trimmedLine);
        } else {
          console.log(`🔄 [DeepSeek] 跳过行: "${trimmedLine}" (不匹配任何模式)`);
        }
      }

      const result = {
        projects: [{
          projectName,
          clientInfo: projectName,
          floors
        }],
        confidence: 0.8,
        warnings: ['使用备用解析方法'],
        rawResponse: content
      };

      console.log('🔄 [DeepSeek] 备用解析结果:', JSON.stringify(result, null, 2));
      console.log('🔄 [DeepSeek] 解析到的楼层数:', floors.length);
      floors.forEach((floor, i) => {
        console.log(`🔄 [DeepSeek] 楼层${i+1}: ${floor.floorName}, 房间数: ${floor.rooms.length}`);
        floor.rooms.forEach((room: any, j: number) => {
          console.log(`🔄 [DeepSeek] 房间${j+1}: ${room.roomName}, 风口数: ${room.vents.length}`);
        });
      });

      return result;
    } catch (error) {
      console.error('🔄 [DeepSeek] 备用解析失败:', error);
      return null;
    }
  }

  /**
   * 创建备用结果（当JSON解析完全失败时）
   */
  private createFallbackResult(content: string, errorMessage: string): AIAnalysisResult {
    console.log('🆘 创建备用结果结构');

    // 尝试从内容中提取基本信息
    const dimensions = content.match(/(\d+)\s*[×xX*]\s*(\d+)/g) || [];
    const vents = dimensions.map((dim: string) => {
      const match = dim.match(/(\d+)\s*[×xX*]\s*(\d+)/);
      if (match) {
        const [, length, width] = match;
        const l = parseInt(length);
        const w = parseInt(width);

        // 智能单位转换
        const correctedL = l < 100 ? l * 10 : l;
        const correctedW = w < 100 ? w * 10 : w;

        // 确保大值作为长度
        const finalLength = Math.max(correctedL, correctedW);
        const finalWidth = Math.min(correctedL, correctedW);

        return {
          systemType: finalWidth < 255 ? 'double_white_outlet' : 'white_return',
          originalType: finalWidth < 255 ? '出风口' : '回风口',
          dimensions: {
            length: finalLength,
            width: finalWidth,
            unit: 'mm'
          },
          color: 'white',
          quantity: 1,
          notes: ''
        };
      }
      return null;
    }).filter(Boolean);

    return {
      projects: [{
        projectName: '未知项目',
        clientInfo: '未知客户',
        floors: [{
          floorName: '1',
          rooms: [{
            roomName: '房间',
            vents: vents as any[]
          }]
        }]
      }],
      confidence: 0.3,
      warnings: [`JSON解析失败: ${errorMessage}`, '使用备用解析方案'],
      rawResponse: content
    };
  }

  /**
   * 检查是否包含风口模式（复制通义千问的逻辑）
   */
  private hasVentPattern(line: string): boolean {
    // 先标准化符号，将各种分隔符统一为×
    const normalizedLine = line
      .replace(/✖️/g, '×')  // 先处理复合字符
      .replace(/[✖✘✕⨯*xX]/g, '×');  // 再处理其他字符
    return /\d+\s*×\s*\d+/.test(normalizedLine) ||
           /(出风|回风|进风|排风|送风)/.test(line);
  }

  /**
   * 检查是否是房间名称（复制通义千问的逻辑）
   */
  private isRoomName(line: string): boolean {
    const roomPatterns = [
      /^(活动室|客厅|茶室|棋牌室|餐厅|厨房|KTV)$/,
      /^[A-Z]\d{3}$/,  // A201, B302等
      /^(主卧|次卧|卧室|书房|办公室|会议室|大厅|走廊|检修口)$/
    ];
    return roomPatterns.some(pattern => pattern.test(line));
  }

  /**
   * 检查是否是有效的项目名称（复制通义千问的逻辑）
   */
  private isValidProjectName(line: string): boolean {
    // 排除尺寸信息
    if (/\d+\s*[×xX*✖️]\s*\d+/.test(line)) return false;
    // 排除风口关键词
    if (/(出风|回风|进风|排风|送风|风口)/.test(line)) return false;
    // 排除纯数字
    if (/^\d+$/.test(line)) return false;
    // 排除单位信息
    if (/(mm|cm|毫米|厘米)/.test(line)) return false;

    // 包含地址关键词或项目特征
    return /[小区花园广场大厦公寓别墅商城中心大楼写字楼苑城府园庄村院街路巷里弄]/.test(line) ||
           /^.{3,15}[A-Z]?$/.test(line); // 3-15字符，可能带字母后缀
  }

  /**
   * 添加风口到楼层结构（复制通义千问的逻辑）
   */
  private addVentToFloors(floors: any[], floorName: string, roomName: string, ventLine: string): void {
    const vent = this.parseVentLine(ventLine);
    if (!vent) return;

    let floor = floors.find(f => f.floorName === `${floorName}楼`);
    if (!floor) {
      floor = { floorName: `${floorName}楼`, rooms: [] };
      floors.push(floor);
    }

    let room = floor.rooms.find((r: any) => r.roomName === roomName);
    if (!room) {
      room = { roomName, vents: [] };
      floor.rooms.push(room);
    }

    room.vents.push(vent);
  }

  /**
   * 解析风口行（复制通义千问的逻辑）
   */
  private parseVentLine(line: string): any | null {
    console.log(`🔄 [DeepSeek] 解析风口行: "${line}"`);

    // 先标准化符号，将各种分隔符统一为×
    const normalizedLine = line
      .replace(/✖️/g, '×')  // 先处理复合字符
      .replace(/[✖✘✕⨯*xX]/g, '×');  // 再处理其他字符
    console.log(`🔄 [DeepSeek] 标准化后: "${normalizedLine}"`);

    // 提取尺寸
    const dimensionMatch = normalizedLine.match(/(\d+)\s*×\s*(\d+)/);
    if (!dimensionMatch) {
      console.log(`🔄 [DeepSeek] 未找到尺寸匹配: "${normalizedLine}"`);
      return null;
    }

    let length = parseInt(dimensionMatch[1]);
    let width = parseInt(dimensionMatch[2]);
    console.log(`🔄 [DeepSeek] 原始尺寸: ${length}×${width}`);

    // 单位转换
    if (length < 100) length *= 10;
    if (width < 100) width *= 10;
    console.log(`🔄 [DeepSeek] 转换后尺寸: ${length}×${width}`);

    // 提取数量
    const quantityMatch = line.match(/(\d+)个/);
    const quantity = quantityMatch ? parseInt(quantityMatch[1]) : 1;
    console.log(`🔄 [DeepSeek] 数量: ${quantity}`);

    // 判断类型
    const hasReturnKeywords = /(回风|进风|排风)/.test(line);
    const systemType = hasReturnKeywords || width >= 255 ? 'white_return' : 'double_white_outlet';
    console.log(`🔄 [DeepSeek] 类型判断: 关键词=${hasReturnKeywords}, 宽度=${width}, 类型=${systemType}`);

    // 提取备注
    const notes = this.extractNotes(line);
    console.log(`🔄 [DeepSeek] 备注: "${notes}"`);

    const result = {
      systemType,
      originalType: line,
      dimensions: { length, width, unit: 'mm' },
      quantity,
      notes
    };

    console.log(`🔄 [DeepSeek] 风口解析结果:`, result);
    return result;
  }

  /**
   * 提取备注信息（复制通义千问的逻辑）
   */
  private extractNotes(line: string): string {
    // 移除尺寸信息后的剩余部分作为备注
    const cleanLine = line.replace(/\d+\s*[×xX*✖️]\s*\d+/, '').trim();
    const notes = cleanLine.replace(/^(出风|回风|进风|排风|送风)(口)?/, '').trim();
    return notes.replace(/^[，,、]/, '').trim();
  }
}

// 创建单例实例
let deepSeekInstance: DeepSeekAPI | null = null;

export const getDeepSeekAPI = (apiKey?: string): DeepSeekAPI => {
  if (!deepSeekInstance) {
    try {
      // 优先使用环境变量配置
      deepSeekInstance = new DeepSeekAPI(apiKey);
    } catch (error) {
      if (apiKey) {
        // 如果环境变量配置失败但提供了apiKey，使用传入的apiKey
        deepSeekInstance = new DeepSeekAPI(apiKey);
      } else {
        throw new Error('DeepSeek API配置失败，请检查环境变量或提供API Key');
      }
    }
  }
  return deepSeekInstance;
};

// 导出默认实例（延迟初始化）
let defaultInstance: DeepSeekAPI | null = null;

export const deepSeekAPI = {
  analyzeVentOrder: async (text: string, forceModel?: string, fastMode: boolean = true) => {
    if (!defaultInstance) {
      try {
        defaultInstance = new DeepSeekAPI();
      } catch (error) {
        // 如果环境变量配置失败，使用硬编码的API Key
        defaultInstance = new DeepSeekAPI('***********************************');
      }
    }
    return defaultInstance.analyzeVentOrder(text, forceModel, fastMode);
  }
};

export type { AIAnalysisResult, ProjectInfo, FloorInfo, RoomInfo, VentInfo };
