/**
 * 清理知识库工具
 */

import { aiKnowledgeBase } from '../services/ai-knowledge-base'

export function clearKnowledgeBase() {
  try {
    // 清空localStorage中的知识库数据
    localStorage.removeItem('ai_knowledge_base')
    console.log('✅ 知识库已清空')
    
    // 重新初始化知识库
    aiKnowledgeBase['loadFromStorage']()
    console.log('✅ 知识库已重新初始化')
    
    return true
  } catch (error) {
    console.error('❌ 清空知识库失败:', error)
    return false
  }
}

// 在浏览器控制台中可以调用
if (typeof window !== 'undefined') {
  (window as any).clearKnowledgeBase = clearKnowledgeBase
}
