# 🇨🇳 风口云平台 - 项目需求文档

## 项目概述
风口云平台是一个专为风口制造工厂设计的综合管理系统，支持客户管理、订单管理、员工管理、财务管理等核心业务功能。

## 技术架构详细说明

### 编程语言
- **TypeScript**: 主要开发语言，提供类型安全
- **JavaScript**: 部分配置文件和工具脚本
- **SQL**: 数据库查询和迁移脚本
- **CSS**: 样式定义（通过Tailwind CSS）
- **JSON**: 配置文件和数据交换格式

### 前端技术栈
- **框架**: Next.js 15.3.3 (React 19) - 最新版本，性能大幅提升
- **语言**: TypeScript 5.x (严格模式)
- **UI组件库**:
  - Tailwind CSS 4.x (最新原子化CSS框架)
  - Radix UI (无障碍UI组件库)
  - Lucide React 0.468.0 (现代化图标库)
- **状态管理**:
  - React Hooks (useState, useEffect, useContext)
  - Zustand 5.0.5 (轻量级状态管理)
- **路由**: Next.js App Router (文件系统路由)
- **样式方案**: Tailwind CSS + CSS Modules
- **构建工具**: Next.js内置Webpack + Turbopack
- **表单处理**: React Hook Form 7.54.0 + Zod 3.24.1 验证

### 后端技术栈
- **运行时**: Node.js 18.20.8 LTS
- **框架**: Next.js 15.3.3 (全栈框架)
- **API架构**: RESTful API (Next.js API Routes)
- **数据库ORM**: Prisma 6.9.0 (类型安全的数据库客户端)
- **认证**: 自定义JWT + bcrypt 3.0.2 密码加密
- **文件处理**:
  - ExcelJS 4.4.0 (Excel文件生成和解析)
  - Sharp 0.34.3 (图像处理)
  - PDF-Parse 1.1.1 (PDF文档解析)
  - XLSX 0.18.5 (Excel文件处理)
- **OCR服务**: 百度智能云OCR API集成
- **数据可视化**: Recharts 2.13.3

### 数据库架构
- **生产环境**: PostgreSQL 13+ (企业级关系数据库)
- **开发环境**: SQLite 3 (轻量级文件数据库)
- **ORM**: Prisma 6.9.0 (类型安全的数据库客户端)
- **迁移管理**: Prisma Migrate (版本化数据库迁移)
- **连接池**: Prisma内置连接池管理
- **数据同步**: 实时数据同步机制
- **备份策略**: 自动定期备份 + 手动备份

### 部署架构
- **服务器**: Alibaba Cloud ECS (弹性计算服务)
- **容器化**: Docker + Docker Compose
- **进程管理**: PM2 (生产环境进程守护)
- **反向代理**: Nginx (负载均衡和SSL终止)
- **域名**: fengkouyun.cn (已备案域名)
- **SSL证书**: Let's Encrypt (免费HTTPS证书)
- **监控**: PM2 Monit + 自定义日志系统
- **CI/CD**: Gitee + 自动部署脚本

## 核心功能模块

### 1. 用户认证与权限管理
- **工厂用户登录**: 支持用户名密码登录
- **密码验证**: 删除操作需要密码确认
- **会话管理**: 防止多地同时登录
- **权限控制**: 基于角色的权限管理

### 2. 客户管理系统
- **客户信息管理**: 姓名、电话、邮箱、公司、地址
- **推荐关系管理**: 支持客户推荐其他客户
- **客户统计**: 自动计算订单数量、金额统计
- **客户删除**: 支持删除客户（需处理关联订单）
- **推荐奖励系统**: 基于推荐关系的奖励计算

### 3. 订单管理系统

#### 订单创建流程详解

**1. 客户信息处理**
- **现有客户选择**:
  - 支持按客户名称或电话号码搜索
  - 下拉列表显示匹配的客户信息
  - 自动填充客户联系方式
- **新客户录入**:
  - 客户名称（必填，2-50字符）
  - 联系电话（必填，7-11位数字）
  - 客户地址（必填）
  - 邮箱（可选，需符合邮箱格式）
  - 推荐人信息（可选，用于奖励计算）

**2. 订单编号生成规则**
- **格式**: 工厂前缀-日期-序号（如：NNJGC-20250628-01）
- **工厂前缀**:
  - 智能提取工厂名称拼音首字母
  - 支持自定义前缀（2-10位大写字母）
  - 自动检查前缀冲突，确保唯一性
- **日期格式**: YYYYMMDD
- **序号**: 当日订单序号，从01开始，支持并发安全
- **唯一性验证**: 自动检查订单号是否重复，重复时自动重试

**3. 快速录单模式（表格式）**
- **楼层分组录入**:
  - 支持按楼层组织产品信息
  - 每个楼层可包含多种风口类型
  - 楼层名称可自定义（如：1F、2F、地下室等）
- **批量操作功能**:
  - 批量设置单价：一键设置整个楼层的所有产品单价
  - 批量复制：复制楼层配置到其他楼层
  - 快速添加：一键添加常用规格的风口
- **实时计算**:
  - 单项金额 = 计算公式结果
  - 楼层小计 = 该楼层所有产品总价
  - 订单总金额 = 所有楼层小计之和

**4. 产品录入界面**
- **产品类型选择**: 下拉选择（普通出风口/回风口/检修口/黑白双色/高端系列）
- **尺寸输入**:
  - 长度（mm，必填）
  - 宽度（mm，普通风口和检修口必填）
  - 高端风口只需要长度
- **数量输入**: 正整数，范围1-9999
- **单价设置**:
  - 自动填充默认单价
  - 支持手动调整
  - 显示计价单位（元/㎡ 或 元/m）
- **总价显示**: 根据计算公式自动计算并显示

**5. 数据验证规则**
- **必填字段检查**:
  - 客户信息完整性
  - 项目地址不能为空
  - 至少包含一个产品项目
- **数值验证**:
  - 尺寸必须为正数
  - 数量必须为正整数
  - 单价必须大于0
- **业务规则验证**:
  - 电话号码格式检查（7-11位数字）
  - 电话号码重复性检查
  - 订单号唯一性验证

### 4. OCR智能识别系统

#### 智能识别引擎架构

**1. 多策略识别引擎**
- **自动内容检测**: 智能判断图片类型（表格/文字/手写）
- **表格识别**: 专门处理表格化的风口清单
- **文字识别**: 处理连续文本格式的风口信息
- **手写识别**: 支持手写风口清单识别
- **多尺度增强**: 图像预处理提高识别准确率
- **空间感知识别**: 基于OCR坐标信息的智能布局分析
- **多尺寸解析**: 支持一行多个尺寸的复杂格式识别

**2. 楼层智能分组功能**
```typescript
// 楼层分析结构
interface FloorAnalysis {
  hasFloors: boolean           // 是否包含楼层信息
  floorPattern: string         // 楼层模式：before_dimension | after_dimension | mixed
  detectedFloors: string[]     // 检测到的楼层列表
  floorPositions: Array<{      // 楼层位置信息
    floor: string
    lineIndex: number
    position: 'before' | 'after'
  }>
}
```

**3. 风口类型智能图片识别**
- **支持20+种风口类型**: 出风口、回风口、线型风口、检修口等
- **关键字映射**: 智能识别风口类型关键字
- **尺寸智能判断**: 基于宽度≥255mm自动判断回风口类型
- **颜色识别**: 支持白色、黑色、双色风口识别
- **材质识别**: 腻子粉预埋、石膏板预埋等特殊材质
- **特殊格式支持**: 斜杠格式(15.5/266.5)、多种分隔符识别
- **智能单位识别**: 自动识别cm/mm单位，智能转换

**4. 多策略解析引擎**
```typescript
// 解析策略
1. 标准格式解析: "类型+尺寸+数量"
2. 反向格式解析: "尺寸+类型+数量"
3. 上下文推断解析: 基于上下文信息推断
4. 楼层后置解析: 处理楼层在尺寸后的情况
5. 模糊恢复解析: 容错处理和数据恢复
6. 多尺寸解析: 一行多个尺寸的智能分割
7. 斜杠格式解析: 15.5/266.5格式的专门处理
8. 组合备注解析: 前缀房间+后缀技术信息组合
```

**5. 智能数据处理**
- **楼层智能分组**: 自动按楼层分组风口
- **相同规格合并**: 智能合并相同楼层的相同尺寸风口
- **数据验证**: 多层次数据验证和错误提示
- **学习优化**: 识别结果学习，持续改进准确率
- **多项目粘贴**: 支持多项目内容的智能分割和识别
- **备注信息提取**: 智能提取房间信息、技术规格等备注
- **单位智能转换**: 自动识别并转换不同单位制

### 5. NLP智能解析系统

#### 多层次文本处理架构

**1. 意图识别引擎 (`/lib/nlp/intent-recognition.ts`)**
- **智能意图识别**: 自动识别用户输入的意图和目标
- **文本预处理**: 清理、标准化输入文本
- **实体提取**: 提取风口类型、尺寸、数量等关键信息
- **置信度计算**: 为识别结果提供可信度评分
- **风口类型判断**: 基于文本和尺寸的综合判断

**2. 排版识别模块 (`/lib/nlp/layout-recognition.ts`)**
- **师傅手写习惯识别**: 识别不同师傅的书写模式
- **分列排版识别**: 处理多列数据的排版格式
- **序号排版识别**: 识别带序号的列表格式
- **表格排版识别**: 处理表格化的数据布局

**3. 空间文本分析器 (`/lib/spatial-text-analyzer.ts`)**
- **OCR坐标分析**: 基于文字在图像中的位置关系
- **空间感知识别**: 理解文本的空间布局和逻辑关系
- **区域分组**: 将相关文本按空间位置分组

**4. 智能表格重组器 (`/lib/intelligent-table-reorganizer.ts`)**
- **混乱数据识别**: 分析OCR识别的混乱文字
- **表格结构推理**: 识别隐含的表格结构规律
- **数据重排**: 将混乱数据按推理规律重新排列

**5. 学习系统模块 (`/lib/nlp/learning-system.ts`)**
- **模式学习**: 模拟机器学习的学习过程
- **结果优化**: 基于历史数据优化识别准确率
- **自适应调整**: 根据用户反馈调整识别策略

#### 支持的文本格式

**1. 标准格式**
```
出风口300×1500mm
回风口120×2750
主卧室回风口300X1200mm
```

**2. 斜杠格式**
```
15.5/266.5    25.5/152.5歺
16/110        25.5/110.5
```

**3. 多项目格式**
```
项目A
一楼
出风口300×1500
二楼
回风口400×300

项目B
客厅出风口250×1200
```

**4. 复杂组合格式**
```
大厅出风口:3090✘150分段
餐厅回风口:300X1230㎜
武宣镇天润城16-1905
```

#### OCR识别流程

**1. 图片预处理**
```typescript
// 图像增强处理
- 图像去噪和锐化
- 对比度和亮度调整
- 文字区域检测和分割
- 多尺度图像生成
```

**2. 智能识别策略**
```typescript
// 识别策略选择
if (检测到表格结构) {
  使用表格识别API
} else if (检测到手写内容) {
  使用手写识别API
} else {
  使用多策略文字识别
}
```

**3. 结果后处理**
```typescript
// 数据清洗和优化
- 楼层信息提取和标准化
- 风口类型智能识别
- 尺寸格式标准化
- 数量信息提取
- 备注信息整理
```

#### 识别准确率优化

**1. 学习系统**
```typescript
interface LearningRecord {
  id: string
  originalText: string      // 原始OCR文本
  correctedData: any[]      // 用户修正后的数据
  learningPoints: Array<{   // 学习要点
    type: 'error' | 'improvement'
    description: string
    pattern: string
  }>
  timestamp: Date
}
```

**2. 错误模式识别**
- 常见识别错误收集
- 错误模式自动修正
- 用户反馈学习机制
- 识别规则动态优化

**3. 质量评分系统**
```typescript
// 识别质量评分
function calculateRecognitionScore(data: any): number {
  let score = 0
  score += data.totalWords * 2        // 识别字数
  score += data.totalChars * 0.1      // 字符数
  score += data.validVents * 10       // 有效风口数
  score += data.floorCount * 5        // 楼层数
  return Math.min(score, 100)
}
```

#### 智能解析核心算法

**1. 风口类型判断规则（统一标准）**
```typescript
// 回风口判断规则（统一为 ≥255mm）
function isReturnVent(text: string, width: number): boolean {
  // 优先级1: 明确关键字
  if (text.includes('回风') || text.includes('进气')) {
    return true;
  }

  // 优先级2: 宽度判断
  if (width >= 255) {
    return true;
  }

  return false;
}

// 风口类型分类
- 线型风口: 宽度 ≤ 160mm
- 出风口: 宽度 160mm - 255mm
- 回风口: 宽度 ≥ 255mm
```

**2. 多尺寸解析算法**
```typescript
// 支持一行多个尺寸的解析
function parseMultipleDimensions(line: string): DimensionResult[] {
  // 匹配所有斜杠格式: 15.5/266.5、25.5/152.5
  const slashMatches = line.match(/(\d+(?:\.\d+)?)\/(\d+(?:\.\d+)?)/g);

  // 智能单位识别: 同一行使用相同单位
  // 小值 < 100 → 整行推断为cm
  // 都 ≥ 100 → 整行推断为mm
}
```

**3. 备注信息组合算法**
```typescript
// 组合提取前缀房间信息 + 后缀技术信息
function extractCombinedNotes(line: string): string {
  const notesParts = [];

  // 提取房间信息: 大厅、餐厅、主卧等
  const roomMatch = line.match(/(大厅|餐厅|主卧|次卧|客厅|厨房|...)/);
  if (roomMatch) notesParts.push(roomMatch[0]);

  // 提取技术规格: 分段、侧出、上出等
  const techMatch = line.match(/(分段|侧出|上出|下出|静音|...)/g);
  if (techMatch) notesParts.push(...techMatch);

  return notesParts.join(' '); // 组合: "大厅 分段"
}
```

### 6. 产品类型与定价系统

#### 产品类型详细定义

**1. 普通出风口 (square)**
- **中文名称**: 普通出风口
- **计算公式**: (长+60mm) × (宽+60mm) ÷ 1,000,000 × 单价(元/㎡) × 数量
- **默认单价**: 150元/㎡
- **标准规格**: 300×300, 400×400, 500×500, 600×600, 800×800mm
- **材质**: 铝合金、不锈钢、塑料
- **颜色**: 白色、银色、黑色
- **安装方式**: 嵌入式、表面式

**2. 普通回风口 (round)**
- **中文名称**: 普通回风口
- **计算公式**: (长+60mm) × (宽+60mm) ÷ 1,000,000 × 单价(元/㎡) × 数量
- **默认单价**: 150元/㎡
- **标准规格**: 同普通出风口
- **特点**: 用于空气回流，结构与出风口类似

**3. 检修口 (maintenance)**
- **中文名称**: 检修口
- **计算公式**: (长+60mm) × (宽+60mm) ÷ 1,000,000 × 单价(元/㎡) × 数量
- **默认单价**: 150元/㎡（与普通风口相同）
- **标准规格**: 400×400, 500×500, 600×600mm
- **特殊功能**: 可拆卸面板，便于维护检修
- **标识**: 订单中明确标注为"检修口"

**4. 黑白双色风口 (rectangular)**
- **中文名称**: 黑白双色风口
- **计算公式**: (长+120mm) ÷ 1,000 × 单价(元/m) × 数量
- **默认单价**: 80元/m
- **特点**: 高端产品，按长度计价
- **外观**: 黑白双色设计，美观大方

**5. 高端风口系列 (custom)**
- **中文名称**: 高端风口系列
- **计算公式**: (长+120mm) ÷ 1,000 × 单价(元/m) × 数量
- **默认单价**: 100元/m
- **包含类型**: 木纹风口、嵌入式风口、箭头风口、爪式风口等
- **特点**: 定制化产品，按长度计价

#### 定价计算公式详解

**1. 普通风口类产品（按面积计价）**
```javascript
// 适用产品：普通出风口、普通回风口、检修口
// 计算公式：(长度+60) × (宽度+60) ÷ 1,000,000 × 单价(元/㎡) × 数量

function calculateSquarePrice(length, width, unitPrice, quantity) {
  if (length <= 0 || width <= 0 || unitPrice <= 0 || quantity <= 0) {
    return 0;
  }
  const area = (length + 60) * (width + 60) / 1000000;
  return area * unitPrice * quantity;
}

// 示例：400×300mm普通出风口，数量2个，单价150元/㎡
// 计算：(400+60) × (300+60) ÷ 1,000,000 × 150 × 2 = 49.68元
```

**2. 高端风口类产品（按长度计价）**
```javascript
// 适用产品：黑白双色风口、高端风口系列
// 计算公式：(长度+120) ÷ 1,000 × 单价(元/m) × 数量

function calculateLinearPrice(length, unitPrice, quantity) {
  if (length <= 0 || unitPrice <= 0 || quantity <= 0) {
    return 0;
  }
  const adjustedLength = (length + 120) / 1000;
  return adjustedLength * unitPrice * quantity;
}

// 示例：600mm黑白双色风口，数量3个，单价80元/m
// 计算：(600+120) ÷ 1,000 × 80 × 3 = 172.8元
```

**3. 订单总金额计算**
```javascript
// 订单总金额 = 所有产品项目的总价之和
订单总金额 = Σ(各楼层所有产品总价)
楼层小计 = Σ(该楼层所有产品总价)
产品总价 = 根据产品类型使用对应计算公式
```

#### 默认单价设置
| 产品类型 | 默认单价 | 计价单位 | 可调整范围 |
|---------|---------|---------|-----------|
| 普通出风口 | 150元 | 元/㎡ | 50-500元/㎡ |
| 普通回风口 | 150元 | 元/㎡ | 50-500元/㎡ |
| 检修口 | 150元 | 元/㎡ | 50-500元/㎡ |
| 黑白双色风口 | 80元 | 元/m | 30-300元/m |
| 高端风口系列 | 100元 | 元/m | 50-500元/m |

#### 快速定价功能
- **批量设置**: 选择楼层批量设置单价
- **自动填充**: 选择产品类型自动填入默认单价
- **实时计算**: 修改数量或单价立即更新总价
- **价格验证**: 确保单价在合理范围内
- **计算预览**: 显示计算公式和中间结果

#### 订单状态管理
**订单状态定义**：
- **待确认 (pending)**: 新创建的订单，等待确认
- **已确认 (confirmed)**: 订单已确认，进入生产流程
- **生产中 (in_production)**: 订单正在生产
- **已完成 (completed)**: 订单生产完成
- **已取消 (cancelled)**: 订单被取消

**状态流转规则**：
- 待确认 → 已确认 → 生产中 → 已完成
- 任何状态都可以 → 已取消
- 已完成和已取消状态不可逆转

### 5. 快速订单创建工作流程

#### 操作界面设计
**1. 客户信息区域**
- 客户选择下拉框（支持搜索）
- 新客户信息录入表单
- 项目地址输入框
- 订单备注文本域

**2. 产品录入区域**
- 楼层标签页（可动态添加/删除）
- 产品录入表格：
  - 产品类型下拉选择
  - 长度输入框（必填）
  - 宽度输入框（普通风口必填）
  - 数量输入框（必填）
  - 单价输入框（可编辑）
  - 总价显示（自动计算）
  - 操作按钮（删除行）

**3. 汇总信息区域**
- 楼层小计显示
- 订单总金额显示
- 保存/提交按钮

#### 快速操作功能
**1. 键盘快捷键**
- Tab键：在输入框间快速切换
- Enter键：确认输入并跳转到下一个字段
- Ctrl+S：快速保存订单
- Ctrl+N：添加新产品行
- Delete键：删除当前选中的产品行

**2. 智能填充**
- 产品类型选择后自动填充默认单价
- 常用规格快速选择（300×300、400×400等）
- 历史订单模板复制功能
- 楼层配置复制到其他楼层

**3. 数据验证提示**
- 实时验证输入数据格式
- 必填字段高亮提示
- 异常数据警告提示
- 重复客户电话提醒

#### 批量操作功能
**1. 楼层管理**
- 添加楼层：点击"+"按钮添加新楼层
- 删除楼层：删除整个楼层及其所有产品
- 复制楼层：将当前楼层配置复制到新楼层
- 楼层重命名：双击楼层名称进行编辑

**2. 产品批量操作**
- 批量设置单价：选择多行产品统一设置单价
- 批量修改数量：按比例调整选中产品的数量
- 批量删除：删除选中的多个产品行
- 快速添加：一键添加常用规格的产品

**3. 模板功能**
- 保存当前订单为模板
- 从模板创建新订单
- 模板管理（编辑、删除模板）
- 常用配置预设

#### 实际界面实现细节
**1. 页面路径**
- 订单列表：`/factory/orders`
- 智能录单：`/factory/orders/create-table`
- 订单详情：`/factory/orders/[id]`
- 编辑订单：`/factory/orders/edit/[id]`

**2. 界面布局**
- **顶部导航**：返回按钮 + 页面标题 + 操作按钮
- **客户信息区**：客户选择/新建 + 项目地址 + 备注
- **产品录入区**：楼层标签页 + 产品表格
- **汇总区域**：楼层小计 + 订单总额 + 保存按钮

**3. 表格列定义**
| 列名 | 字段类型 | 必填 | 说明 |
|------|---------|------|------|
| 产品类型 | 下拉选择 | ✓ | square/round/maintenance/rectangular/custom |
| 长度(mm) | 数字输入 | ✓ | 正整数，范围10-9999 |
| 宽度(mm) | 数字输入 | 条件必填 | 普通风口和检修口必填 |
| 数量 | 数字输入 | ✓ | 正整数，范围1-9999 |
| 单价 | 数字输入 | ✓ | 可编辑，显示计价单位 |
| 总价 | 自动计算 | - | 实时计算显示 |
| 操作 | 按钮 | - | 删除行按钮 |

**4. 数据流转**
```javascript
// 订单保存流程
1. 前端验证 → 2. 构建订单数据 → 3. 调用数据库服务 → 4. 返回结果
orderData = {
  factoryId: 当前工厂ID,
  clientId: 客户ID,
  orderNumber: 自动生成的订单号,
  items: 产品项目数组,
  totalAmount: 订单总金额,
  status: 'pending',
  projectAddress: 项目地址,
  notes: 备注,
  clientName: 客户名称,
  clientPhone: 客户电话
}
```

**5. 错误处理**
- 数据验证失败：高亮错误字段，显示提示信息
- 网络错误：显示重试按钮
- 保存失败：保留用户输入，提示重新保存
- 客户电话重复：提示选择现有客户或修改电话

### 6. 数据格式与API接口

#### 订单数据结构
```typescript
interface Order {
  id: string                    // 订单ID
  factoryId: string            // 工厂ID
  clientId: string             // 客户ID
  orderNumber: string          // 订单编号（如：NNJGC-20250628-01）
  items: OrderItem[]           // 订单项目列表
  totalAmount: number          // 订单总金额
  status: 'pending' | 'confirmed' | 'in_production' | 'completed' | 'cancelled'
  projectAddress: string       // 项目地址
  notes?: string              // 备注
  clientName: string          // 客户名称（冗余字段，便于显示）
  clientPhone: string         // 客户电话（冗余字段，便于显示）
  createdAt: Date            // 创建时间
  updatedAt: Date            // 更新时间
  createdBy?: string         // 创建人ID
}

interface OrderItem {
  id: string                  // 项目ID
  productName: string         // 产品名称
  productType: 'square' | 'round' | 'rectangular' | 'custom' | 'maintenance'
  specifications: string      // 规格说明
  dimensions: {              // 尺寸信息
    length?: number          // 长度(mm)
    width?: number           // 宽度(mm)
  }
  floor?: string             // 楼层信息
  quantity: number           // 数量
  unitPrice: number          // 单价
  totalPrice: number         // 总价
  calculatedBy?: string      // 计算公式说明
  notes?: string            // 备注
}
```

#### 价格计算API
```typescript
// 价格计算函数（前端使用）
function calculateVentPrice(item: {
  type: string,
  length: number,
  width: number,
  quantity: number,
  unitPrice: number
}): number {
  if (unitPrice <= 0) return 0;

  let calculatedPrice = 0;

  if (type === 'square' || type === 'round' || type === 'maintenance') {
    // 普通风口和检修口：(长+60) × (宽+60) ÷ 1,000,000 × 单价 × 数量
    if (length > 0 && width > 0) {
      const area = (length + 60) * (width + 60) / 1000000;
      calculatedPrice = area * unitPrice * quantity;
    }
  } else {
    // 高端风口：(长+120) ÷ 1,000 × 单价 × 数量
    if (length > 0) {
      calculatedPrice = ((length + 120) / 1000) * unitPrice * quantity;
    }
  }

  return Math.round(calculatedPrice * 10) / 10; // 保留1位小数，四舍五入
}
```

#### 默认单价配置
```typescript
interface DefaultUnitPrices {
  square: number      // 普通出风口默认单价（元/㎡）
  round: number       // 普通回风口默认单价（元/㎡）
  rectangular: number // 黑白双色风口默认单价（元/m）
  custom: number      // 高端风口系列默认单价（元/m）
  maintenance: number // 检修口默认单价（元/㎡）
}

// 默认值
const DEFAULT_UNIT_PRICES: DefaultUnitPrices = {
  square: 150,      // 150元/㎡
  round: 150,       // 150元/㎡
  rectangular: 80,  // 80元/m
  custom: 100,      // 100元/m
  maintenance: 150  // 150元/㎡
}
```

#### 订单编号生成规则
```typescript
// 订单编号格式：工厂前缀-日期-序号
// 示例：NNJGC-20250628-01

function generateOrderNumber(factoryName: string, date: Date): string {
  // 1. 提取工厂名称拼音首字母作为前缀
  const prefix = extractPinyinInitials(factoryName);

  // 2. 格式化日期为YYYYMMDD
  const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');

  // 3. 获取当日订单序号（从01开始）
  const sequence = getDailySequence(prefix, dateStr);

  // 4. 组合生成订单号
  return `${prefix}-${dateStr}-${sequence.toString().padStart(2, '0')}`;
}
```

### 7. 财务管理系统

#### 付款状态管理
- **未付款 (unpaid)**: 订单金额 = 0
- **部分付款 (partial)**: 0 < 已付金额 < 订单金额
- **已付款 (paid)**: 已付金额 >= 订单金额

#### 财务计算公式
```javascript
// 客户财务统计
客户总订单金额 = Σ(该客户所有订单金额)
客户已付金额 = Σ(该客户所有付款记录)
客户未付金额 = 客户总订单金额 - 客户已付金额

// 工厂财务统计
月度营业额 = Σ(当月所有订单金额)
月度回款额 = Σ(当月所有付款记录)
应收账款 = Σ(所有未付订单金额)

// 推荐奖励计算
推荐奖励 = 被推荐客户已付金额 × 奖励比例
奖励比例 = {
  "第1-5个客户": 2%,
  "第6-10个客户": 3%,
  "第11个客户以上": 5%
}
```

#### 账单管理功能
- **自动生成**: 按月/季/年自动生成账单
- **账单内容**:
  - 订单明细
  - 付款记录
  - 余额信息
  - 项目汇总
- **Excel导出**:
  - 客户账单导出
  - 财务汇总报表
  - 应收账款明细
  - 推荐奖励统计

### 6. 股东管理与利润分析系统

#### 股东信息管理

**1. 股东档案管理**
```typescript
interface Shareholder {
  id: string                    // 股东ID
  factoryId: string            // 关联工厂ID
  name: string                 // 股东姓名
  phone?: string               // 联系电话
  email?: string               // 邮箱地址
  address?: string             // 联系地址
  shareholderType: ShareholderType  // 股东类型
  shareCount: number           // 持股数量
  sharePercentage: number      // 股权比例
  investmentAmount: number     // 投资金额
  joinDate: Date              // 入股日期
  exitDate?: Date             // 退股日期
  status: ShareholderStatus   // 股东状态
  notes?: string              // 备注信息
}

enum ShareholderType {
  FOUNDER   // 创始股东
  INVESTOR  // 投资股东
  EMPLOYEE  // 员工股东
  PARTNER   // 合伙人股东
}

enum ShareholderStatus {
  active    // 在职
  inactive  // 暂停
  exited    // 已退出
}
```

**2. 股权结构管理**
- **股权比例计算**: 基于投资金额自动计算股权比例
- **股份数量管理**: 假设每股1元，股份数=投资金额
- **股权变更记录**: 详细记录每次股权变更历史
- **股权验证**: 确保总股权比例不超过100%

#### 投资管理系统

**1. 追加投资功能**
```typescript
interface InvestmentData {
  amount: number                    // 投资金额
  notes?: string                   // 投资备注
  adjustmentType: 'auto' | 'manual' // 调整类型
  newSharePercentage?: number      // 手动设置的新股权比例
}
```

**2. 股权调整模式**

**自动调整模式（科学稀释）**:
```typescript
// 计算公式
股权比例 = 个人投资额 / 总投资额 × 100%

// 适用场景：按投资金额公平分配股权
function autoAdjustSharePercentages(factoryId: string) {
  const shareholders = getActiveShareholdersByFactory(factoryId)
  const totalInvestment = shareholders.reduce((sum, sh) => sum + sh.investmentAmount, 0)

  shareholders.forEach(shareholder => {
    const newPercentage = (shareholder.investmentAmount / totalInvestment) * 100
    updateShareholderPercentage(shareholder.id, newPercentage)
  })
}
```

**手动调整模式**:
```typescript
// 用户直接指定新的股权比例
// 适用场景：技术入股、管理股等特殊情况
function manualAdjustSharePercentage(shareholderId: string, newPercentage: number) {
  // 验证股权比例合理性
  validateSharePercentage(shareholderId, newPercentage)

  // 更新股权比例
  updateShareholderPercentage(shareholderId, newPercentage)
}
```

#### 利润分配系统

**1. 分红管理**
```typescript
interface Dividend {
  id: string
  factoryId: string
  title: string                // 分红标题
  description?: string         // 分红说明
  totalAmount: number         // 总分红金额
  baseAmount: number          // 分红基数
  dividendRate: number        // 分红比例
  periodStart: Date           // 分红期间开始
  periodEnd: Date             // 分红期间结束
  status: DividendStatus      // 分红状态
  approvedAt?: Date           // 审批时间
  distributedAt?: Date        // 分配时间
}

enum DividendStatus {
  pending     // 待审批
  approved    // 已审批
  distributed // 已分配
  cancelled   // 已取消
}
```

**2. 分红计算逻辑**
```typescript
// 股东分红金额计算
function calculateDividendAmount(dividend: Dividend, shareholder: Shareholder): number {
  return dividend.totalAmount * (shareholder.sharePercentage / 100)
}

// 分红记录生成
function generateDividendRecords(dividend: Dividend): DividendRecord[] {
  const activeShareholders = getActiveShareholdersByFactory(dividend.factoryId)

  return activeShareholders.map(shareholder => ({
    dividendId: dividend.id,
    shareholderId: shareholder.id,
    amount: calculateDividendAmount(dividend, shareholder),
    percentage: shareholder.sharePercentage,
    status: 'pending'
  }))
}
```

#### 投资回报分析

**1. ROI计算**
```typescript
// 投资回报率计算
function calculateROI(shareholder: Shareholder, timeRange: DateRange): number {
  const totalDividends = getTotalDividends(shareholder.id, timeRange)
  const investmentAmount = shareholder.investmentAmount

  return (totalDividends / investmentAmount) * 100
}

// 年化收益率计算
function calculateAnnualizedReturn(shareholder: Shareholder): number {
  const daysSinceInvestment = getDaysBetween(shareholder.joinDate, new Date())
  const totalReturn = calculateROI(shareholder, { start: shareholder.joinDate, end: new Date() })

  return (totalReturn / daysSinceInvestment) * 365
}
```

**2. 利润分析报表**
- **股东利润分配表**: 按股东统计分红收益
- **投资回报分析**: ROI和年化收益率分析
- **股权变更历史**: 股权变更时间线
- **分红历史记录**: 历史分红明细

#### 数据验证与安全

**1. 股权比例验证**
```typescript
function validateSharePercentage(shareholderId: string, newPercentage: number): boolean {
  // 验证范围
  if (newPercentage < 0 || newPercentage > 100) {
    throw new Error('股权比例必须在0-100之间')
  }

  // 验证总和
  const otherShareholdersTotal = getOtherShareholdersTotal(shareholderId)
  if (otherShareholdersTotal + newPercentage > 100) {
    throw new Error('总股权比例不能超过100%')
  }

  return true
}
```

**2. 操作权限控制**
- **股东创建**: 仅管理员和工厂所有者
- **股权调整**: 需要特殊权限验证
- **分红审批**: 多级审批流程
- **数据导出**: 敏感数据访问控制

### 7. 公告系统
- **公告发布**: 支持工厂内部公告
- **已读状态**: 公告阅读状态持久化
- **公告管理**: 公告的增删改查

### 8. 技术架构与模块组织

#### 前端技术栈
- **框架**: Next.js 14 (App Router)
- **UI组件**: Tailwind CSS + Shadcn/ui
- **状态管理**: React Hooks + Context API
- **表单处理**: React Hook Form + Zod验证
- **图片处理**: 支持多种格式上传和预览

#### 后端技术栈
- **数据库**: PostgreSQL (Supabase)
- **认证**: Supabase Auth
- **文件存储**: Supabase Storage
- **API**: Next.js API Routes
- **OCR服务**: 百度OCR API集成

#### 核心模块架构

**1. 智能解析模块层次**
```
用户输入 (文本/图片)
    ↓
OCR识别层 (/lib/enhanced-ocr-parser.ts)
    ↓
空间分析层 (/lib/spatial-text-analyzer.ts)
    ↓
表格重组层 (/lib/intelligent-table-reorganizer.ts)
    ↓
NLP解析层 (/lib/nlp/intent-recognition.ts)
    ↓
数据验证层 (/lib/utils/dimension-utils.ts)
    ↓
订单生成层 (/lib/nlp/smart-order-generator.ts)
```

**2. 文件组织结构**
```
src/
├── app/                    # Next.js App Router
│   ├── (factory)/         # 工厂用户路由组
│   └── api/               # API路由
├── lib/                   # 核心业务逻辑
│   ├── nlp/              # NLP智能解析模块
│   ├── utils/            # 工具函数
│   └── services/         # 服务层
├── components/           # UI组件
│   ├── ui/              # 基础UI组件
│   └── factory/         # 业务组件
└── types/               # TypeScript类型定义
```

**3. 数据流架构**
```
用户操作 → 组件状态 → API调用 → 数据库操作 → 响应返回 → UI更新
```

## 数据库设计详细说明

### 数据库选型说明
- **开发环境**: SQLite 3 (文件数据库，便于本地开发)
- **生产环境**: PostgreSQL 13+ (企业级关系数据库)
- **ORM工具**: Prisma 6.9.0 (类型安全的数据库访问层)
- **连接方式**: 连接池管理，支持并发访问
- **字符编码**: UTF-8 (支持中文字符)
- **数据同步**: 实时数据同步机制
- **备份策略**: 自动定期备份 + 手动备份

### 核心数据表结构

#### 1. Factory (工厂信息表)
```sql
CREATE TABLE Factory (
  id          VARCHAR PRIMARY KEY,
  name        VARCHAR NOT NULL,           -- 工厂名称
  code        VARCHAR UNIQUE NOT NULL,    -- 工厂代码 (如: CMCEV0)
  address     VARCHAR,                    -- 工厂地址
  phone       VARCHAR,                    -- 联系电话
  email       VARCHAR,                    -- 邮箱地址
  created_at  TIMESTAMP DEFAULT NOW(),
  updated_at  TIMESTAMP DEFAULT NOW()
);
```

#### 2. FactoryUser (工厂用户表)
```sql
CREATE TABLE FactoryUser (
  id            VARCHAR PRIMARY KEY,
  factory_id    VARCHAR NOT NULL,         -- 关联工厂ID
  username      VARCHAR UNIQUE NOT NULL,  -- 用户名 (如: sh002)
  password_hash VARCHAR NOT NULL,         -- 加密密码
  name          VARCHAR NOT NULL,         -- 真实姓名 (如: 李四)
  email         VARCHAR,                  -- 邮箱
  phone         VARCHAR,                  -- 电话
  role          VARCHAR DEFAULT 'employee', -- 角色
  permissions   TEXT,                     -- JSON格式权限
  is_active     BOOLEAN DEFAULT true,     -- 是否激活
  created_at    TIMESTAMP DEFAULT NOW(),
  updated_at    TIMESTAMP DEFAULT NOW(),
  FOREIGN KEY (factory_id) REFERENCES Factory(id)
);
```

#### 3. Client (客户信息表)
```sql
CREATE TABLE Client (
  id            VARCHAR PRIMARY KEY,
  factory_id    VARCHAR NOT NULL,         -- 关联工厂ID
  name          VARCHAR NOT NULL,         -- 客户姓名
  phone         VARCHAR NOT NULL,         -- 客户电话
  email         VARCHAR,                  -- 客户邮箱
  company       VARCHAR,                  -- 公司名称
  address       VARCHAR,                  -- 客户地址
  referrer_id   VARCHAR,                  -- 推荐人ID
  referrer_name VARCHAR,                  -- 推荐人姓名
  status        VARCHAR DEFAULT 'active', -- 客户状态
  created_at    TIMESTAMP DEFAULT NOW(),
  updated_at    TIMESTAMP DEFAULT NOW(),
  FOREIGN KEY (factory_id) REFERENCES Factory(id),
  FOREIGN KEY (referrer_id) REFERENCES Client(id)
);
```

#### 4. Order (订单信息表)
```sql
CREATE TABLE Order (
  id              VARCHAR PRIMARY KEY,
  factory_id      VARCHAR NOT NULL,       -- 关联工厂ID
  client_id       VARCHAR NOT NULL,       -- 关联客户ID
  order_number    VARCHAR UNIQUE NOT NULL, -- 订单号
  items           TEXT NOT NULL,          -- JSON格式订单项目
  total_amount    DECIMAL(10,2) NOT NULL, -- 订单总金额
  status          VARCHAR DEFAULT 'pending', -- 订单状态
  project_address VARCHAR,                -- 项目地址
  client_name     VARCHAR,                -- 客户姓名(冗余字段)
  client_phone    VARCHAR,                -- 客户电话(冗余字段)
  notes           TEXT,                   -- 订单备注
  created_by      VARCHAR NOT NULL,       -- 录单员ID
  created_by_name VARCHAR,                -- 录单员姓名
  created_at      TIMESTAMP DEFAULT NOW(),
  updated_at      TIMESTAMP DEFAULT NOW(),
  FOREIGN KEY (factory_id) REFERENCES Factory(id),
  FOREIGN KEY (client_id) REFERENCES Client(id),
  FOREIGN KEY (created_by) REFERENCES FactoryUser(id)
);
```

#### 5. Payment (付款记录表)
```sql
CREATE TABLE Payment (
  id           VARCHAR PRIMARY KEY,
  order_id     VARCHAR NOT NULL,          -- 关联订单ID
  client_id    VARCHAR NOT NULL,          -- 关联客户ID
  amount       DECIMAL(10,2) NOT NULL,    -- 付款金额
  payment_date TIMESTAMP NOT NULL,        -- 付款日期
  method       VARCHAR,                   -- 付款方式
  notes        TEXT,                      -- 付款备注
  created_at   TIMESTAMP DEFAULT NOW(),
  FOREIGN KEY (order_id) REFERENCES Order(id),
  FOREIGN KEY (client_id) REFERENCES Client(id)
);
```

### 数据格式规范

#### JSON数据格式
1. **订单项目 (Order.items)**
```json
[
  {
    "id": "item_001",
    "productName": "普通风口",
    "specifications": "600x600",
    "quantity": 10,
    "unitPrice": 45.50,
    "totalPrice": 455.00,
    "floor": "1楼",
    "notes": "备注信息"
  }
]
```

2. **用户权限 (FactoryUser.permissions)**
```json
["order_entry", "client_management", "financial_view"]
```

#### 枚举值定义
1. **订单状态 (OrderStatus)**
   - `pending`: 待处理
   - `in_progress`: 进行中
   - `completed`: 已完成
   - `cancelled`: 已取消

2. **付款状态 (PaymentStatus)**
   - `unpaid`: 未付款
   - `partial`: 部分付款
   - `paid`: 已付款

3. **客户状态 (ClientStatus)**
   - `active`: 活跃
   - `inactive`: 非活跃

### 数据一致性要求
- **外键约束**: 确保数据引用完整性
- **级联删除**: 客户删除时处理关联数据
- **事务支持**: 关键操作使用数据库事务
- **索引优化**: 关键查询字段建立索引
- **数据验证**: 应用层和数据库层双重验证

## 用户界面要求

### 前端架构设计
- **组件化架构**: 基于React函数组件
- **页面路由**: Next.js App Router (文件系统路由)
- **状态管理**: React Hooks + Context API
- **样式系统**: Tailwind CSS + CSS Modules
- **类型安全**: TypeScript严格模式

### API架构设计
- **RESTful API**: 标准REST接口设计
- **路由结构**: `/api/[resource]/[action]`
- **请求格式**: JSON格式数据交换
- **响应格式**: 统一的响应结构
- **错误处理**: 标准HTTP状态码 + 错误信息

### 设计原则
- **简洁美观**: 避免界面混乱，减少不必要按钮
- **中文界面**: 所有界面元素使用中文
- **响应式设计**: 支持不同屏幕尺寸
- **打印友好**: 支持清洁的打印布局
- **无障碍访问**: 支持键盘导航和屏幕阅读器

### 关键页面架构
1. **登录页面** (`/factory/login`)
   - 用户名密码验证
   - 会话管理
   - 错误提示

2. **仪表板** (`/factory/dashboard`)
   - 数据概览卡片
   - 快速操作入口
   - 实时统计信息

3. **客户管理** (`/factory/clients`)
   - 客户列表展示
   - 搜索和筛选
   - 增删改查操作
   - 推荐关系管理

4. **订单管理** (`/factory/orders`)
   - 订单列表和详情
   - 快速录单界面
   - 状态管理
   - 打印和导出

5. **财务管理** (`/factory/finance`)
   - 账单汇总
   - 付款记录
   - Excel导出
   - 统计报表

## 详细业务流程设计

### 快速录单界面设计 (`/factory/orders/create-table`)

#### 界面布局
```
┌─────────────────────────────────────────────────────────────┐
│ 🏢 新建订单 - 表格录入模式                                    │
├─────────────────────────────────────────────────────────────┤
│ 客户信息: [下拉选择] 或 [新客户录入]                          │
│ 项目地址: [___________________________]                     │
│ 订单备注: [___________________________]                     │
├─────────────────────────────────────────────────────────────┤
│ 📋 产品录入表格                                              │
│ ┌─────┬────────┬────────┬──────┬──────┬──────┬──────────┐    │
│ │楼层 │ 产品类型│ 规格   │ 数量 │ 单价 │ 小计 │ 操作     │    │
│ ├─────┼────────┼────────┼──────┼──────┼──────┼──────────┤    │
│ │1楼  │普通风口│600x600 │ [10] │[45.5]│ 455  │[批量设价]│    │
│ │1楼  │检修口  │600x600 │ [2]  │[45.5]│ 91   │[删除]    │    │
│ │2楼  │普通风口│500x500 │ [8]  │[42.0]│ 336  │[复制楼层]│    │
│ └─────┴────────┴────────┴──────┴──────┴──────┴──────────┘    │
│                                        总计: ¥882.00        │
├─────────────────────────────────────────────────────────────┤
│ [+ 添加楼层] [批量导入] [保存草稿] [提交订单]                 │
└─────────────────────────────────────────────────────────────┘
```

#### 快速录入操作
1. **键盘快捷键**:
   - `Tab`: 切换到下一个输入框
   - `Enter`: 确认输入并移动到下一行
   - `Ctrl+D`: 复制当前行
   - `Ctrl+S`: 快速保存
   - `F2`: 批量设置当前楼层单价

2. **智能填充**:
   - 选择产品类型自动填入建议单价
   - 输入规格自动匹配标准单价
   - 复制楼层功能快速录入相似楼层

3. **实时验证**:
   - 数量必须大于0
   - 单价必须大于0
   - 规格必须符合标准格式
   - 重复项目自动提醒

#### 批量操作功能
1. **批量设置单价**:
   ```javascript
   // 选择楼层 → 输入单价 → 应用到该楼层所有风口
   function batchSetPrice(floorId, price) {
     updateFloorVents(floorId, { unitPrice: price })
     recalculateFloorTotal(floorId)
   }
   ```

2. **楼层复制**:
   ```javascript
   // 复制楼层配置到新楼层
   function copyFloor(sourceFloor, targetFloorName) {
     const newFloor = {
       ...sourceFloor,
       id: generateId(),
       name: targetFloorName
     }
     addFloor(newFloor)
   }
   ```

3. **模板导入**:
   - 支持Excel模板导入
   - CSV格式数据导入
   - 历史订单模板复用

### 数据验证规则详细说明

#### 客户信息验证
```javascript
// 客户信息验证规则
const clientValidation = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 50,
    pattern: /^[\u4e00-\u9fa5a-zA-Z\s]+$/ // 中文、英文、空格
  },
  phone: {
    required: true,
    pattern: /^1[3-9]\d{9}$/ // 中国手机号格式
  },
  email: {
    required: false,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ // 邮箱格式
  }
}
```

#### 订单数据验证
```javascript
// 订单项目验证规则
const orderItemValidation = {
  productName: {
    required: true,
    enum: ["普通风口", "检修口", "防火风口"]
  },
  specifications: {
    required: true,
    pattern: /^\d{3}x\d{3}$/, // 格式：300x300
    enum: ["300x300", "400x400", "500x500", "600x600", "800x800"]
  },
  quantity: {
    required: true,
    min: 1,
    max: 9999,
    type: "integer"
  },
  unitPrice: {
    required: true,
    min: 0.01,
    max: 999999.99,
    precision: 2 // 保留2位小数
  }
}
```

#### 业务规则验证
```javascript
// 订单业务规则
const businessRules = {
  // 订单号唯一性检查
  orderNumberUnique: async (orderNumber) => {
    const exists = await checkOrderNumberExists(orderNumber)
    return !exists
  },

  // 客户信用检查
  customerCreditCheck: (customerId) => {
    const unpaidAmount = getCustomerUnpaidAmount(customerId)
    const creditLimit = getCustomerCreditLimit(customerId)
    return unpaidAmount <= creditLimit
  },

  // 最小订单金额检查
  minimumOrderAmount: (totalAmount) => {
    return totalAmount >= 100 // 最小订单金额100元
  }
}
```

### 用户体验优化细节

#### 智能提示功能
1. **自动完成**:
   - 客户姓名输入时显示匹配的历史客户
   - 项目地址输入时提示历史地址
   - 规格输入时自动补全标准规格

2. **实时计算显示**:
   ```javascript
   // 实时计算逻辑
   function updateCalculations() {
     // 单项计算
     item.totalPrice = item.quantity * item.unitPrice

     // 楼层小计
     floor.subtotal = floor.items.reduce((sum, item) => sum + item.totalPrice, 0)

     // 订单总计
     order.totalAmount = order.floors.reduce((sum, floor) => sum + floor.subtotal, 0)

     // 更新界面显示
     updateUI()
   }
   ```

3. **错误提示优化**:
   - 字段级实时验证
   - 友好的错误提示信息
   - 错误字段高亮显示
   - 批量错误汇总提示

#### 数据保存策略
1. **自动保存草稿**:
   - 每30秒自动保存到本地存储
   - 页面刷新时恢复草稿数据
   - 提交成功后清除草稿

2. **离线支持**:
   - 网络断开时保存到本地
   - 网络恢复时自动同步
   - 冲突检测和解决机制

### 订单状态流转管理

#### 状态定义和流转规则
```javascript
// 订单状态枚举
const OrderStatus = {
  PENDING: 'pending',       // 待处理
  IN_PROGRESS: 'in_progress', // 进行中
  COMPLETED: 'completed',   // 已完成
  CANCELLED: 'cancelled'    // 已取消
}

// 状态流转规则
const statusTransitions = {
  pending: ['in_progress', 'cancelled'],
  in_progress: ['completed', 'cancelled'],
  completed: [], // 已完成不能再变更
  cancelled: []  // 已取消不能再变更
}

// 状态变更权限
const statusPermissions = {
  pending: ['admin', 'manager', 'employee'],
  in_progress: ['admin', 'manager'],
  completed: ['admin', 'manager'],
  cancelled: ['admin']
}
```

#### 状态变更触发事件
```javascript
// 状态变更时的业务逻辑
function onStatusChange(orderId, newStatus, oldStatus) {
  switch(newStatus) {
    case 'in_progress':
      // 开始生产，锁定订单内容
      lockOrderContent(orderId)
      notifyProductionTeam(orderId)
      break

    case 'completed':
      // 完成订单，更新库存，生成发货单
      updateInventory(orderId)
      generateShippingDocument(orderId)
      updateCustomerStatistics(orderId)
      break

    case 'cancelled':
      // 取消订单，释放库存，记录取消原因
      releaseInventory(orderId)
      recordCancellationReason(orderId)
      break
  }
}
```

### 打印和导出功能详细设计

#### 订单打印模板
```html
<!-- 订单打印模板 -->
<div class="print-template">
  <header class="print-header">
    <h1>风口订单确认单</h1>
    <div class="order-info">
      <p>订单号: {{orderNumber}}</p>
      <p>日期: {{createDate}}</p>
    </div>
  </header>

  <section class="customer-info">
    <h2>客户信息</h2>
    <p>客户姓名: {{customerName}}</p>
    <p>联系电话: {{customerPhone}}</p>
    <p>项目地址: {{projectAddress}}</p>
  </section>

  <section class="order-items">
    <h2>订单明细</h2>
    <table>
      <thead>
        <tr>
          <th>楼层</th>
          <th>产品类型</th>
          <th>规格</th>
          <th>数量</th>
          <th>单价</th>
          <th>小计</th>
        </tr>
      </thead>
      <tbody>
        {{#each items}}
        <tr>
          <td>{{floor}}</td>
          <td>{{productName}}</td>
          <td>{{specifications}}</td>
          <td>{{quantity}}</td>
          <td>¥{{unitPrice}}</td>
          <td>¥{{totalPrice}}</td>
        </tr>
        {{/each}}
      </tbody>
      <tfoot>
        <tr>
          <td colspan="5">总计</td>
          <td>¥{{totalAmount}}</td>
        </tr>
      </tfoot>
    </table>
  </section>

  <footer class="print-footer">
    <p>录单员: {{createdByName}}</p>
    <p>制单时间: {{createdAt}}</p>
  </footer>
</div>
```

#### Excel导出功能
```javascript
// Excel导出配置
const excelExportConfig = {
  // 订单明细导出
  orderDetails: {
    filename: '订单明细_{{orderNumber}}_{{date}}.xlsx',
    sheets: [
      {
        name: '订单信息',
        data: 'orderInfo'
      },
      {
        name: '产品明细',
        data: 'orderItems'
      }
    ]
  },

  // 财务报表导出
  financialReport: {
    filename: '财务报表_{{startDate}}_{{endDate}}.xlsx',
    sheets: [
      {
        name: '订单汇总',
        data: 'orderSummary'
      },
      {
        name: '客户账单',
        data: 'customerBills'
      },
      {
        name: '应收账款',
        data: 'receivables'
      }
    ]
  }
}
```

### 系统集成和扩展性

#### API接口设计规范
```javascript
// RESTful API设计规范
const apiEndpoints = {
  // 订单相关
  'GET /api/orders': '获取订单列表',
  'POST /api/orders': '创建新订单',
  'GET /api/orders/:id': '获取订单详情',
  'PUT /api/orders/:id': '更新订单',
  'DELETE /api/orders/:id': '删除订单',

  // 客户相关
  'GET /api/clients': '获取客户列表',
  'POST /api/clients': '创建新客户',
  'PUT /api/clients/:id': '更新客户信息',
  'DELETE /api/clients/:id': '删除客户',

  // 财务相关
  'GET /api/payments': '获取付款记录',
  'POST /api/payments': '记录付款',
  'GET /api/reports/financial': '财务报表',
  'GET /api/export/excel': 'Excel导出'
}
```

#### 扩展功能预留
1. **库存管理模块**: 预留产品库存管理接口
2. **生产管理模块**: 预留生产计划和进度管理
3. **物流管理模块**: 预留发货和物流跟踪
4. **移动端支持**: 预留移动端API接口
5. **第三方集成**: 预留ERP系统集成接口

## 部署与运维要求

### 开发环境配置
- **操作系统**: Windows 11 / macOS / Linux
- **Node.js版本**: 18.20.8 LTS
- **包管理器**: npm 9.x
- **数据库**: SQLite 3 (文件: `prisma/dev.db`)
- **开发端口**: 3000
- **热重载**: Next.js Fast Refresh
- **环境变量**: `.env.local` 文件配置
- **开发工具**:
  - VS Code (推荐IDE)
  - Prisma Studio (数据库管理)
  - Chrome DevTools (调试)

### 生产环境配置
- **服务器**: Alibaba Cloud ECS
- **操作系统**: CentOS 7 / Ubuntu 20.04
- **Node.js版本**: 18.20.8
- **数据库**: PostgreSQL 13
- **进程管理**: PM2 5.x
- **域名**: fengkouyun.cn
- **SSL证书**: Let's Encrypt
- **反向代理**: Nginx (可选)
- **监控**: PM2 Monit + 自定义日志

### 项目文件结构
```
factorysystem/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (factory)/         # 工厂管理页面
│   │   ├── api/               # API路由
│   │   └── globals.css        # 全局样式
│   ├── components/            # React组件
│   │   ├── ui/               # 基础UI组件
│   │   ├── clients/          # 客户管理组件
│   │   ├── orders/           # 订单管理组件
│   │   └── common/           # 通用组件
│   ├── lib/                  # 工具库
│   │   ├── database/         # 数据库操作
│   │   ├── api/              # API客户端
│   │   └── utils/            # 工具函数
│   └── types/                # TypeScript类型定义
├── prisma/                   # 数据库配置
│   ├── schema.prisma         # 数据库模式
│   └── migrations/           # 数据库迁移
├── public/                   # 静态资源
├── package.json              # 项目依赖
├── tsconfig.json            # TypeScript配置
├── tailwind.config.js       # Tailwind CSS配置
└── next.config.js           # Next.js配置
```

### 数据安全与备份
- **密码加密**: bcrypt (salt rounds: 12)
- **会话管理**: JWT + 安全cookie
- **数据验证**: Zod schema验证
- **SQL注入防护**: Prisma ORM参数化查询
- **XSS防护**: React内置XSS防护
- **CSRF防护**: SameSite cookie策略
- **数据备份**:
  - 生产环境: 每日自动备份
  - 开发环境: Git版本控制
- **访问控制**: 基于角色的权限系统

## 特殊需求

### 业务逻辑
- **订单号生成**: 基于工厂英文代码的自动编号
- **推荐奖励**: 阶梯式奖励计算系统
- **客户统计**: 实时更新客户的订单统计
- **数据导出**: 支持Excel格式导出

### 性能要求
- **响应时间**: 页面加载时间 < 3秒
- **并发支持**: 支持多用户同时操作
- **数据一致性**: 确保数据操作的原子性

### 兼容性要求
- **浏览器**: 支持现代浏览器（Chrome、Firefox、Edge）
- **设备**: 支持桌面和平板设备
- **网络**: 支持低带宽环境

## 开发规范

### 代码规范详细说明

#### 编码标准
- **语言**: TypeScript (严格模式)
- **代码风格**: ESLint + Prettier
- **注释语言**: 所有注释必须使用中文
- **命名规范**:
  - 变量/函数: camelCase (如: `getUserInfo`)
  - 组件: PascalCase (如: `ClientList`)
  - 常量: UPPER_SNAKE_CASE (如: `API_BASE_URL`)
  - 文件名: kebab-case (如: `client-list.tsx`)

#### 文件组织规范
- **组件文件**: `.tsx` 扩展名
- **工具文件**: `.ts` 扩展名
- **样式文件**: `.module.css` 或 Tailwind classes
- **类型定义**: 统一在 `src/types/` 目录
- **API接口**: 统一在 `src/app/api/` 目录

#### 错误处理规范
- **前端错误**: try-catch + 用户友好提示
- **API错误**: 标准HTTP状态码 + 错误消息
- **数据库错误**: Prisma错误处理 + 日志记录
- **日志格式**:
  ```typescript
  console.log('✅ 操作成功:', data)
  console.error('❌ 操作失败:', error)
  console.warn('⚠️ 警告信息:', warning)
  ```

#### TypeScript类型安全
- **严格模式**: `strict: true`
- **类型定义**: 所有函数参数和返回值必须有类型
- **接口定义**: 统一的数据接口定义
- **泛型使用**: 适当使用泛型提高代码复用性

### 测试策略
- **单元测试**: Jest + React Testing Library
- **集成测试**: API接口自动化测试
- **端到端测试**: Playwright (关键业务流程)
- **性能测试**: Lighthouse CI
- **用户验收测试**: 手动测试关键功能

## 维护与更新

### 版本管理
- **代码仓库**: Gitee (https://gitee.com/lao62025/factorysystem)
- **分支策略**: 主分支 + 功能分支
- **部署流程**: 推送到Gitee → 服务器拉取 → 重启服务

### 监控与日志
- **应用监控**: PM2进程监控
- **错误日志**: 详细的错误日志记录
- **性能监控**: 数据库查询性能监控

---

## 项目核心特色功能 🌟

### 🤖 智能图片识别技术栈
1. **多层次解析架构**: OCR → 空间分析 → 表格重组 → NLP解析 → 数据验证
2. **多格式支持**: 标准格式、斜杠格式、多项目格式、复杂组合格式
3. **智能单位识别**: 自动识别cm/mm单位，智能转换统一
4. **空间感知识别**: 基于文字坐标的智能布局分析
5. **学习优化系统**: 持续学习用户修正，提升识别准确率

### 📊 业务流程优化
1. **一键智能录单**: 图片上传 → 自动识别 → 智能分组 → 快速确认
2. **楼层智能分组**: 自动识别楼层信息，相同规格智能合并
3. **多尺寸批量处理**: 一行多个尺寸自动分割为多个风口
4. **实时价格计算**: 支持5种计价模式，实时计算总价
5. **批量操作支持**: 批量设价、楼层复制、快速添加

### 🎯 行业专业特性
1. **风口类型全覆盖**: 支持20+种风口类型智能识别
2. **统一判断标准**: 回风口判断规则统一为≥255mm
3. **专业计价公式**: 普通风口按面积计价，高端风口按长度计价
4. **行业术语识别**: 智能识别"分段"、"侧出"等专业术语
5. **师傅手写适配**: 专门优化师傅手写习惯的识别

### 💡 用户体验创新
1. **零学习成本**: 直接粘贴文本或上传图片即可使用
2. **容错性强**: 支持各种不规范格式的智能修正
3. **实时反馈**: 识别过程实时显示，结果即时预览
4. **一键导出**: 支持Excel、PDF等多种格式导出
5. **移动端适配**: 支持手机拍照识别和移动端操作

## 当前已实现功能 ✅

### 🔐 用户认证与权限管理
- **多层级认证系统**: 管理员、工厂用户分级管理
- **单点登录控制**: 防止多地同时登录，会话管理
- **登录记录追踪**: 详细的登录日志和统计分析
- **权限控制**: 基于角色的细粒度权限管理
- **密码安全**: bcrypt加密，安全验证机制

### 👥 客户管理系统
- **完整客户信息管理**: 姓名、电话、邮箱、公司、地址
- **推荐关系管理**: 支持客户推荐其他客户，推荐链追踪
- **客户统计分析**: 自动计算订单数量、金额统计
- **推荐奖励系统**: 阶梯式奖励计算（2%-5%）
- **客户删除**: 支持删除客户并处理关联订单
- **数据导出**: Excel格式客户信息导出

### 📋 订单管理系统
- **智能订单创建**: 表格式快速录单，支持楼层分组
- **订单编号生成**: 自动生成唯一订单号（工厂前缀-日期-序号）
- **产品类型支持**: 5种风口类型，精确计价公式
- **订单状态管理**: 待确认→已确认→生产中→已完成
- **批量操作**: 批量设置单价、楼层复制、快速添加
- **实时计算**: 自动计算单项金额、楼层小计、订单总额
- **订单打印**: 专业订单确认单打印模板
- **数据验证**: 多层次数据验证和错误提示

### 🤖 OCR智能识别系统
- **多策略识别引擎**: 表格识别、文字识别、手写识别
- **智能内容检测**: 自动判断图片类型，选择最佳识别方法
- **楼层智能分组**: 自动识别楼层信息，智能分组合并
- **风口类型识别**: 支持20+种风口类型智能识别
- **多尺度增强**: 图像预处理，提高识别准确率
- **学习系统**: 识别结果学习优化，持续改进
- **批量处理**: 支持多文件批量识别处理

### 💰 财务管理系统
- **付款状态管理**: 未付款、部分付款、已付款状态追踪
- **财务统计**: 月度营业额、回款额、应收账款统计
- **账单管理**: 自动生成客户账单，支持Excel导出
- **推荐奖励计算**: 自动计算推荐奖励，支持现金提取
- **财务报表**: 多维度财务分析报表

### 🏢 股东管理与利润分析
- **股东信息管理**: 完整的股东档案和联系信息
- **股权结构管理**: 股权比例、股份数量动态管理
- **投资记录追踪**: 详细的投资历史和变更记录
- **智能股权调整**: 自动调整和手动调整两种模式
- **利润分配系统**: 基于股权比例的自动利润分配
- **分红管理**: 分红创建、审批、分配全流程管理
- **投资回报分析**: ROI计算和趋势分析

### 👨‍💼 员工管理
- **员工档案管理**: 完整的员工信息管理
- **角色权限分配**: 灵活的角色和权限配置
- **工作记录**: 员工操作日志和工作统计

### 📢 公告系统
- **公告发布**: 支持工厂内部公告发布
- **已读状态**: 公告阅读状态持久化跟踪
- **公告管理**: 公告的增删改查完整功能

### 📊 数据分析与报表
- **实时仪表板**: 关键业务指标实时展示
- **趋势分析**: 销售趋势、客户增长数据分析
- **多格式导出**: Excel、PDF等格式报表导出
- **数据可视化**: 图表化展示业务数据

### 🔄 数据同步与备份
- **实时数据同步**: 多工厂数据实时同步机制
- **自动备份**: 定期数据备份和恢复
- **数据一致性**: 确保数据完整性和一致性

### 🖥️ 系统管理
- **工厂状态监控**: 自动检查工厂订阅状态
- **系统健康检查**: 定期系统状态检查
- **日志管理**: 详细的系统操作日志
- **性能监控**: 系统性能指标监控

## 最近重要更新 🆕

### 2025年1月更新
- **多尺寸解析引擎**: 新增支持一行多个尺寸的智能解析（如：15.5/266.5 25.5/152.5）
- **回风口判断规则统一**: 将所有模块的回风口判断阈值统一为≥255mm
- **NLP智能解析系统**: 完整的多层次文本处理架构，支持复杂格式识别
- **空间感知识别**: 基于OCR坐标信息的智能布局分析
- **智能备注组合**: 自动组合前缀房间信息和后缀技术规格
- **斜杠格式支持**: 专门支持15.5/266.5格式的尺寸识别
- **单位智能识别**: 同一行尺寸自动使用相同单位制

### 2024年12月更新
- **OCR智能识别系统重大升级**: 新增多策略识别引擎，识别准确率提升30%
- **楼层智能分组功能**: 自动识别楼层信息，智能合并相同规格风口
- **股东管理系统完善**: 新增手动股权调整、投资记录追踪功能
- **订单创建优化**: 修复Chunk加载错误，提升用户体验
- **单点登录增强**: 防止多地同时登录，增强账户安全
- **项目文件清理**: 清理66个临时文件，项目结构更加整洁

### 技术架构升级
- **Next.js 15.3.3**: 升级到最新版本，性能提升
- **React 19**: 使用最新React版本，更好的性能和开发体验
- **Prisma 6.9.0**: 数据库ORM升级，更好的类型安全
- **TypeScript 5**: 严格模式，更好的类型检查

## 待优化功能 🔄

### 短期优化计划
- **订单删除功能**: 完善订单删除逻辑和权限控制
- **检修口产品类型**: 优化检修口产品的识别和计价
- **录单员姓名显示**: 优化订单中录单员信息显示
- **移动端适配**: 优化移动设备访问体验
- **API性能优化**: 提升API响应速度

### 中期发展计划
- **库存管理模块**: 产品库存管理和预警
- **生产管理模块**: 生产计划和进度跟踪
- **物流管理模块**: 发货和物流跟踪
- **客户端APP**: 移动端原生应用开发
- **第三方集成**: ERP系统集成接口

### 长期发展规划
- **AI智能推荐**: 基于历史数据的智能推荐
- **大数据分析**: 行业数据分析和预测
- **云原生架构**: 微服务架构升级
- **国际化支持**: 多语言和多地区支持

## 项目统计信息 📈

### 代码规模统计
- **总代码行数**: 约50,000行
- **TypeScript文件**: 200+ 个
- **React组件**: 150+ 个
- **API接口**: 80+ 个
- **数据库表**: 15+ 个

### 功能模块统计
- **核心业务模块**: 8个主要模块
- **智能解析引擎**: 5个解析层次
- **支持风口类型**: 20+ 种
- **支持文本格式**: 10+ 种
- **数据导出格式**: 5种

### 技术特性统计
- **识别准确率**: 95%+
- **支持图片格式**: JPG、PNG、PDF等
- **最大文件大小**: 10MB
- **并发用户支持**: 100+
- **响应时间**: <3秒

### 开发历程
- **项目启动**: 2024年6月
- **核心功能完成**: 2024年10月
- **智能识别升级**: 2024年12月
- **NLP系统完善**: 2025年1月
- **持续优化中**: 进行中...
