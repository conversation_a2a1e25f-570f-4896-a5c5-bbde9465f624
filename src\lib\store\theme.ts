/**
 * 🇨🇳 风口云平台 - 主题管理Store
 * 
 * 功能说明：
 * - 管理应用主题状态
 * - 支持浅色、深色、自动主题
 * - 持久化主题设置
 * - 提供多种预设主题
 */

import { create } from 'zustand'
import { persist } from 'zustand/middleware'

// 主题类型定义
export type ThemeMode = 'light' | 'dark' | 'auto' | 'smart'

// 预设主题配置
export const THEME_PRESETS = {
  light: {
    name: '浅色主题',
    description: '适合白天使用的明亮主题',
    colors: {
      background: 'hsl(0 0% 100%)',
      foreground: 'hsl(222.2 84% 4.9%)',
      card: 'hsl(0 0% 100%)',
      cardForeground: 'hsl(222.2 84% 4.9%)',
      popover: 'hsl(0 0% 100%)',
      popoverForeground: 'hsl(222.2 84% 4.9%)',
      primary: 'hsl(221.2 83.2% 53.3%)',
      primaryForeground: 'hsl(210 40% 98%)',
      secondary: 'hsl(210 40% 96%)',
      secondaryForeground: 'hsl(222.2 84% 4.9%)',
      muted: 'hsl(210 40% 96%)',
      mutedForeground: 'hsl(215.4 16.3% 46.9%)',
      accent: 'hsl(210 40% 96%)',
      accentForeground: 'hsl(222.2 84% 4.9%)',
      destructive: 'hsl(0 84.2% 60.2%)',
      destructiveForeground: 'hsl(210 40% 98%)',
      border: 'hsl(214.3 31.8% 91.4%)',
      input: 'hsl(214.3 31.8% 91.4%)',
      ring: 'hsl(221.2 83.2% 53.3%)',
    }
  },
  dark: {
    name: '深色主题',
    description: '适合夜间使用的深色主题',
    colors: {
      background: 'hsl(0 0% 3.9%)',        // 更深的黑色背景
      foreground: 'hsl(0 0% 98%)',         // 纯白色文字
      card: 'hsl(0 0% 3.9%)',              // 卡片背景与主背景一致
      cardForeground: 'hsl(0 0% 98%)',     // 卡片文字
      popover: 'hsl(0 0% 3.9%)',           // 弹出层背景
      popoverForeground: 'hsl(0 0% 98%)',  // 弹出层文字
      primary: 'hsl(210 40% 98%)',         // 主色调改为白色
      primaryForeground: 'hsl(0 0% 9%)',   // 主色调文字为深色
      secondary: 'hsl(0 0% 14.9%)',        // 次要背景
      secondaryForeground: 'hsl(0 0% 98%)', // 次要文字
      muted: 'hsl(0 0% 14.9%)',            // 静音背景
      mutedForeground: 'hsl(0 0% 63.9%)',  // 静音文字
      accent: 'hsl(0 0% 14.9%)',           // 强调背景
      accentForeground: 'hsl(0 0% 98%)',   // 强调文字
      destructive: 'hsl(0 62.8% 30.6%)',   // 危险色保持不变
      destructiveForeground: 'hsl(0 0% 98%)', // 危险色文字
      border: 'hsl(0 0% 14.9%)',           // 边框颜色
      input: 'hsl(0 0% 14.9%)',            // 输入框背景
      ring: 'hsl(0 0% 83.1%)',             // 焦点环颜色
    }
  },
  blue: {
    name: '蓝色主题',
    description: '专业的蓝色商务主题',
    colors: {
      background: 'hsl(0 0% 100%)',
      foreground: 'hsl(222.2 84% 4.9%)',
      primary: 'hsl(221.2 83.2% 53.3%)',
      primaryForeground: 'hsl(210 40% 98%)',
      secondary: 'hsl(210 40% 96%)',
      accent: 'hsl(210 40% 96%)',
    }
  },
  green: {
    name: '绿色主题',
    description: '清新的绿色自然主题',
    colors: {
      background: 'hsl(0 0% 100%)',
      foreground: 'hsl(222.2 84% 4.9%)',
      primary: 'hsl(142.1 76.2% 36.3%)',
      primaryForeground: 'hsl(355.7 100% 97.3%)',
      secondary: 'hsl(210 40% 96%)',
      accent: 'hsl(210 40% 96%)',
    }
  },
  purple: {
    name: '紫色主题',
    description: '优雅的紫色创意主题',
    colors: {
      background: 'hsl(0 0% 100%)',
      foreground: 'hsl(222.2 84% 4.9%)',
      primary: 'hsl(262.1 83.3% 57.8%)',
      primaryForeground: 'hsl(210 40% 98%)',
      secondary: 'hsl(210 40% 96%)',
      accent: 'hsl(210 40% 96%)',
    }
  }
}

interface ThemeState {
  mode: ThemeMode
  isSystemDark: boolean
  isTimeDark: boolean
  smartThemeSettings: {
    lightStartHour: number  // 浅色主题开始时间（如：6点）
    darkStartHour: number   // 深色主题开始时间（如：18点）
  }
}

interface ThemeActions {
  setMode: (mode: ThemeMode) => void
  setSystemDark: (isDark: boolean) => void
  getCurrentTheme: () => typeof THEME_PRESETS.light
  applyTheme: () => void
}

type ThemeStore = ThemeState & ThemeActions

const initialState: ThemeState = {
  mode: 'light',
  isSystemDark: false
}

export const useThemeStore = create<ThemeStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      setMode: (mode) => {
        set({ mode })
        get().applyTheme()
      },



      setSystemDark: (isSystemDark) => {
        set({ isSystemDark })
        get().applyTheme()
      },

      getCurrentTheme: () => {
        const { mode, isSystemDark } = get()

        // 确定当前应该使用的主题模式
        let effectiveMode = mode
        if (mode === 'auto') {
          effectiveMode = isSystemDark ? 'dark' : 'light'
        }

        // 获取对应的主题
        const theme = THEME_PRESETS[effectiveMode as keyof typeof THEME_PRESETS]

        return theme || THEME_PRESETS.light
      },

      applyTheme: () => {
        if (typeof window === 'undefined') return

        try {
          // 延迟执行，确保DOM完全准备好
          requestAnimationFrame(() => {
            const { mode, isSystemDark } = get()
            const root = document.documentElement

            // 设置dark类，但不设置CSS变量到html元素上
            const isDark = mode === 'dark' || (mode === 'auto' && isSystemDark)

            if (isDark) {
              root.classList.add('dark')
            } else {
              root.classList.remove('dark')
            }

            console.log('🎨 主题已应用:', isDark ? '深色模式' : '浅色模式')
          })
        } catch (error) {
          console.error('❌ 主题应用失败:', error)
        }
      }
    }),
    {
      name: 'theme-storage',
      partialize: (state) => ({
        mode: state.mode
      })
    }
  )
)

// 监听系统主题变化（仅在客户端）
if (typeof window !== 'undefined') {
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')

  const handleSystemThemeChange = (e: MediaQueryListEvent) => {
    useThemeStore.getState().setSystemDark(e.matches)
  }

  mediaQuery.addEventListener('change', handleSystemThemeChange)

  // 初始化系统主题状态
  useThemeStore.getState().setSystemDark(mediaQuery.matches)
}
