/**
 * 🇨🇳 风口云平台 - 智能意图识别系统
 *
 * 基于NLP技术识别OCR文本中的订单意图，自动创建风口订单
 * 集成师傅手写习惯的智能排版识别
 */

import { recognizeLayout, LayoutRecognitionResult } from './layout-recognition'
import { TableLayoutAnalyzer, TableLayout } from '@/lib/table-layout-analyzer'

// 风口类型映射
const VENT_TYPE_MAPPING = {
  // 出风口关键词
  出风: 'double_white_outlet',
  出风口: 'double_white_outlet',
  送风: 'double_white_outlet',
  送风口: 'double_white_outlet',
  
  // 回风口关键词
  回风: 'white_return',
  回风口: 'white_return',
  
  // 线型风口关键词
  线型: 'white_linear',
  线形: 'white_linear',
  条形: 'white_linear',
  
  // 检修口关键词
  检修: 'maintenance',
  检修口: 'maintenance',
  维修: 'maintenance',
  维修口: 'maintenance'
}

// 颜色映射
const COLOR_MAPPING = {
  白: 'white',
  白色: 'white',
  黑: 'black',
  黑色: 'black',
  象牙白: 'white'
}

// 数字识别映射（包括中文数字）
const NUMBER_MAPPING = {
  一: 1, 二: 2, 三: 3, 四: 4, 五: 5, 六: 6, 七: 7, 八: 8, 九: 9, 十: 10,
  壹: 1, 贰: 2, 叁: 3, 肆: 4, 伍: 5, 陆: 6, 柒: 7, 捌: 8, 玖: 9, 拾: 10,
  '1': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '0': 0
}

// 意图识别结果
export interface IntentRecognitionResult {
  intent: 'create_order' | 'query_price' | 'unknown'
  confidence: number
  entities: {
    projectName?: string
    floorInfo?: string
    ventItems: VentItemIntent[]
    customerInfo?: {
      name?: string
      phone?: string
      address?: string
    }
  }
  layout?: LayoutRecognitionResult // 新增排版识别结果
  rawText: string
}

// 风口项目意图
export interface VentItemIntent {
  type: string
  length: number
  width: number
  quantity: number
  color?: string
  notes?: string
  confidence: number
  originalText: string
}

/**
 * 基于表格布局的智能意图识别（新增）
 */
export function recognizeIntentWithTable(text: string, ocrResult: any, enableDebug: boolean = false): IntentRecognitionResult {
  console.log('🧠 开始基于表格布局的智能意图识别...')
  console.log('📝 输入文本:', text)

  const startTime = Date.now()

  // 预处理文本
  console.log('🔧 步骤1: 文本预处理...')
  const cleanText = preprocessText(text)

  // 表格布局分析
  console.log('📊 步骤2: 表格布局分析...')
  let tableLayout: TableLayout | null = null
  try {
    tableLayout = TableLayoutAnalyzer.analyzeTableLayout(ocrResult)
    console.log(`📊 表格分析: ${tableLayout.columns.length} 列, ${tableLayout.rows.length} 行`)
  } catch (error) {
    console.warn('⚠️ 表格分析失败，回退到传统方法:', error)
  }

  // 智能排版识别
  console.log('📐 步骤3: 智能排版识别...')
  const layoutResult = recognizeLayout(cleanText)

  // 识别意图类型
  console.log('🎯 步骤4: 意图分类...')
  const intent = classifyIntent(cleanText)

  // 提取实体信息（优先使用表格布局）
  console.log('📊 步骤5: 智能实体提取...')
  const entities = tableLayout
    ? extractEntitiesWithTable(cleanText, tableLayout, layoutResult)
    : extractEntitiesWithLayout(cleanText, layoutResult)

  // 计算整体置信度
  console.log('📈 步骤6: 置信度计算...')
  const confidence = calculateOverallConfidence(entities.ventItems)

  const processingTime = Date.now() - startTime
  console.log(`⏱️ 处理完成，耗时: ${processingTime}ms`)

  const result: IntentRecognitionResult = {
    intent,
    confidence,
    entities,
    layout: layoutResult,
    rawText: text
  }

  console.log('✅ 基于表格的意图识别完成:', {
    intent: result.intent,
    confidence: (result.confidence * 100).toFixed(1) + '%',
    ventItemsCount: result.entities.ventItems.length,
    tableColumns: tableLayout?.columns.length || 0,
    processingTime: processingTime + 'ms'
  })

  return result
}

/**
 * 智能意图识别主函数
 */
export function recognizeIntent(text: string, enableDebug: boolean = false): IntentRecognitionResult {
  console.log('🧠 开始智能意图识别...')
  console.log('📝 输入文本:', text)

  const startTime = Date.now()

  // 预处理文本
  console.log('🔧 步骤1: 文本预处理...')
  const cleanText = preprocessText(text)
  if (enableDebug) {
    console.log('   原始长度:', text.length)
    console.log('   处理后长度:', cleanText.length)
    console.log('   处理后文本:', cleanText.substring(0, 100) + '...')
  }

  // 新增: 智能排版识别
  console.log('📐 步骤1.5: 智能排版识别...')
  const layoutResult = recognizeLayout(cleanText)
  if (enableDebug) {
    console.log('   排版类型:', layoutResult.layoutType)
    console.log('   排版置信度:', (layoutResult.confidence * 100).toFixed(1) + '%')
    console.log('   风口分组:', layoutResult.ventGroups.length, '组')
  }

  // 识别意图类型
  console.log('🎯 步骤2: 意图分类...')
  const intent = classifyIntent(cleanText)
  if (enableDebug) {
    console.log('   识别意图:', intent)
  }

  // 提取实体信息（基于排版识别结果）
  console.log('📊 步骤3: 智能实体提取...')
  const entities = extractEntitiesWithLayout(cleanText, layoutResult)
  if (enableDebug) {
    console.log('   项目名称:', entities.projectName || '未识别')
    console.log('   楼层信息:', entities.floorInfo || '未识别')
    console.log('   风口项目数:', entities.ventItems.length)
    console.log('   排版优化:', layoutResult.ventGroups.length, '个分组')
  }

  // 计算整体置信度
  console.log('📈 步骤4: 置信度计算...')
  const confidence = calculateOverallConfidence(entities.ventItems)
  if (enableDebug) {
    console.log('   整体置信度:', (confidence * 100).toFixed(1) + '%')
  }

  const processingTime = Date.now() - startTime
  console.log(`⏱️ 处理完成，耗时: ${processingTime}ms`)

  const result: IntentRecognitionResult = {
    intent,
    confidence,
    entities,
    layout: layoutResult, // 包含排版识别结果
    rawText: text
  }

  console.log('✅ 意图识别完成:', {
    intent: result.intent,
    confidence: (result.confidence * 100).toFixed(1) + '%',
    ventItemsCount: result.entities.ventItems.length,
    processingTime: processingTime + 'ms'
  })

  return result
}

/**
 * 文本预处理（增强版：保留换行符，更好地清理无用信息）
 */
function preprocessText(text: string): string {
  console.log('🔧 开始文本预处理...')

  let processed = text
    // 统一换行符
    .replace(/\r\n/g, '\n')
    .replace(/\r/g, '\n')

    // 按行处理，保留换行结构
    .split('\n')
    .map(line => {
      return line
        // 清理等号和无用符号
        .replace(/\s*=\s*/g, ' ') // 等号转空格
        .replace(/\s*[\+\-\*\/]\s*(?!\d)/g, ' ') // 非数学运算的符号转空格

        // 统一乘号和尺寸分隔符
        .replace(/[×X*]/g, 'x')
        .replace(/✖️/g, 'x') // 特殊的乘号符号

        // 清理单位信息
        .replace(/(\d+)\s*(mm|㎜|毫米)/g, '$1') // 移除单位，保留数字

        // 去除多余空格
        .replace(/\s+/g, ' ')

        // 统一标点符号
        .replace(/[。，；：]/g, '.')

        // 清理行首行尾的无用字符
        .replace(/^[\s\.\-\+\*\/=]+/, '')
        .replace(/[\s\.\-\+\*\/=]+$/, '')
        .trim()
    })
    .filter(line => line.length > 0) // 过滤空行
    .join('\n')
    .trim()

  console.log(`🔧 预处理完成: ${text.length} → ${processed.length} 字符`)
  console.log(`🔧 处理后行数: ${processed.split('\n').length}`)
  return processed
}

/**
 * 意图分类
 */
function classifyIntent(text: string): 'create_order' | 'query_price' | 'unknown' {
  // 订单创建关键词
  const orderKeywords = ['出风', '回风', '风口', 'x', '×', '数量', '个', '楼', '层']
  const priceKeywords = ['价格', '多少钱', '报价', '单价']
  
  const orderScore = orderKeywords.filter(keyword => text.includes(keyword)).length
  const priceScore = priceKeywords.filter(keyword => text.includes(keyword)).length
  
  if (orderScore >= 2) return 'create_order'
  if (priceScore >= 1) return 'query_price'
  return 'unknown'
}

/**
 * 基于表格布局的智能实体提取（新增）
 */
function extractEntitiesWithTable(text: string, tableLayout: TableLayout, layoutResult: LayoutRecognitionResult): IntentRecognitionResult['entities'] {
  console.log('   🎯 基于表格布局进行智能实体提取...')

  const entities: IntentRecognitionResult['entities'] = {
    ventItems: []
  }

  // 从表格中提取项目名称和楼层信息
  entities.projectName = extractProjectNameFromTable(tableLayout)
  entities.floorInfo = extractFloorInfoFromTable(tableLayout)

  // 从表格数据行提取风口项目
  entities.ventItems = extractVentItemsFromTable(tableLayout)

  console.log(`   ✅ 表格智能提取完成: ${entities.ventItems.length}个风口项目`)
  return entities
}

/**
 * 基于排版识别的智能实体提取
 */
function extractEntitiesWithLayout(text: string, layoutResult: LayoutRecognitionResult): IntentRecognitionResult['entities'] {
  console.log('   🎯 基于排版识别进行智能实体提取...')

  const lines = text.split('\n').filter(line => line.trim())

  const entities: IntentRecognitionResult['entities'] = {
    ventItems: []
  }

  // 提取项目名称（通常在第一行或包含地址信息）
  entities.projectName = extractProjectName(lines)

  // 提取楼层信息
  entities.floorInfo = extractFloorInfo(lines)

  // 基于排版结果提取风口项目
  entities.ventItems = extractVentItemsFromLayout(layoutResult)

  // 应用房间上下文增强（使用原始文本而不是cleanText）
  entities.ventItems = enhanceWithRoomContext(entities.ventItems, text)

  console.log(`   ✅ 智能提取完成: ${entities.ventItems.length}个风口项目`)
  return entities
}

/**
 * 传统实体提取（保留兼容性）
 */
function extractEntities(text: string): IntentRecognitionResult['entities'] {
  const lines = text.split('\n').filter(line => line.trim())

  const entities: IntentRecognitionResult['entities'] = {
    ventItems: []
  }

  // 提取项目名称（通常在第一行或包含地址信息）
  entities.projectName = extractProjectName(lines)

  // 提取楼层信息
  entities.floorInfo = extractFloorInfo(lines)

  // 提取风口项目
  entities.ventItems = extractVentItems(lines)

  // 应用房间上下文增强（使用原始文本）
  entities.ventItems = enhanceWithRoomContext(entities.ventItems, text)

  return entities
}

/**
 * 基于排版识别结果提取风口项目（优化版：支持序号备注）
 */
function extractVentItemsFromLayout(layoutResult: LayoutRecognitionResult): VentItemIntent[] {
  console.log('     🔍 基于排版结果提取风口项目...')

  const ventItems: VentItemIntent[] = []

  for (const group of layoutResult.ventGroups) {
    console.log(`     📦 处理分组: ${group.title} (${group.items.length}个项目)`)

    for (const item of group.items) {
      const dimensionParts = item.dimensions.split(/[xX×*]/).map(s => parseInt(s.trim()))

      // 验证尺寸数据有效性
      if (dimensionParts.length !== 2 || dimensionParts.some(isNaN) || dimensionParts.some(val => val <= 0)) {
        console.log(`     ❌ 跳过无效尺寸: "${item.dimensions}" -> [${dimensionParts.join(', ')}]`)
        continue
      }

      const [length, width] = dimensionParts

      if (length > 0 && width > 0) {
        // 根据分组类型和尺寸智能判断风口类型
        const type = determineVentTypeFromLayout(group, item, width)

        // 智能尺寸排序
        const finalLength = Math.max(length, width)
        const finalWidth = Math.min(length, width)

        // 数量处理
        const quantity = item.quantity || 1

        // 置信度计算（基于排版识别的置信度更高）
        const confidence = Math.min(item.confidence + 0.1, 1.0)

        // 智能备注生成（优先级：item.notes > 楼层信息 > 序号信息）
        const notes = generateSmartNotes(item, layoutResult.structure.sequencePattern)

        const ventItem: VentItemIntent = {
          type,
          length: finalLength,
          width: finalWidth,
          quantity,
          notes,
          confidence,
          originalText: item.originalText
        }

        ventItems.push(ventItem)

        const notesDisplay = notes ? ` (备注: ${notes})` : ''
        console.log(`       ✅ 创建风口: ${type} ${finalLength}x${finalWidth} x${quantity}${notesDisplay} (置信度: ${(confidence * 100).toFixed(1)}%)`)
      }
    }
  }

  // 添加排版相关的警告
  if (layoutResult.warnings.length > 0) {
    console.log('     ⚠️ 排版警告:', layoutResult.warnings.join(', '))
  }

  return ventItems
}

/**
 * 智能生成备注信息
 */
function generateSmartNotes(item: any, sequencePatterns: string[]): string | undefined {
  const notes: string[] = []

  // 优先级1: 已有的备注信息
  if (item.notes) {
    notes.push(item.notes)
  }

  // 优先级2: 楼层信息（如果有楼层关键词）
  if (item.floor) {
    notes.push(item.floor)
  }

  // 优先级3: 序号信息（当没有楼层信息时作为备注）
  if (item.sequence && !item.floor) {
    // 检查是否为列式排版
    const isColumnLayout = sequencePatterns.some(pattern => pattern.includes('列式'))
    if (isColumnLayout) {
      notes.push(`位置${item.sequence}`)
    } else {
      notes.push(`序号${item.sequence}`)
    }
  }

  return notes.length > 0 ? notes.join(' ') : undefined
}

/**
 * 根据排版分组确定风口类型
 */
function determineVentTypeFromLayout(group: any, item: any, width: number): string {
  console.log(`       🎯 根据排版确定风口类型: 分组=${group.type}, 宽度=${width}mm`)

  // 如果分组已经明确类型
  if (group.type === 'outlet') {
    console.log('       📋 分组指定为出风口')
    return 'double_white_outlet'
  }

  if (group.type === 'return') {
    console.log('       📋 分组指定为回风口')
    return 'white_return'
  }

  // 检查原文中的关键词
  const line = item.originalText.toLowerCase()
  for (const [keyword, type] of Object.entries(VENT_TYPE_MAPPING)) {
    if (line.includes(keyword)) {
      console.log(`       🔤 关键词匹配: "${keyword}" → ${type}`)
      return type
    }
  }

  // 基于宽度智能判断（优化判断逻辑）
  if (width >= 255) {
    console.log(`       📏 宽度判断: ${width}mm ≥ 255mm → 回风口`)
    return 'white_return'
  } else if (width <= 160) {
    console.log(`       📏 宽度判断: ${width}mm ≤ 160mm → 线型风口`)
    return 'white_linear'
  }

  console.log(`       📏 宽度判断: ${width}mm 在160-255mm之间 → 出风口`)
  return 'double_white_outlet'
}

/**
 * 提取项目名称
 */
function extractProjectName(lines: string[]): string | undefined {
  for (const line of lines.slice(0, 3)) { // 检查前3行
    // 如果包含地址关键词但不包含风口尺寸信息，可能是项目名
    if ((line.includes('小区') || line.includes('大厦') || line.includes('广场') ||
         line.includes('花园') || line.includes('苑') || line.includes('城')) &&
        !containsVentDimensions(line)) {
      return line.trim()
    }
  }
  return undefined
}

/**
 * 检查文本是否包含风口尺寸信息
 */
function containsVentDimensions(text: string): boolean {
  // 更全面的尺寸匹配模式，支持多种分隔符和单位
  const dimensionPatterns = [
    /\d+\s*[xX×✖️✖✘✕*+\-]\s*\d+\s*(?:mm|㎜|毫米)?/,  // 标准尺寸格式（扩展分隔符）
    /\d+\s*乘\s*\d+/,                                    // 中文格式
    /直径\s*\d+/,                                        // 圆形风口
  ]

  // 同时检查是否包含风口相关关键词，避免误判地址中的数字组合
  const hasVentKeywords = /风口|出风|回风|进气|检修|维修/.test(text)
  const hasDimensions = dimensionPatterns.some(pattern => pattern.test(text))

  // 只有同时包含风口关键词和尺寸信息才认为是风口尺寸
  return hasVentKeywords && hasDimensions
}

/**
 * 提取楼层信息（增强版）
 */
function extractFloorInfo(lines: string[]): string | undefined {
  console.log('🏢 开始提取楼层信息...')

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    if (!line) continue

    console.log(`   检查第${i+1}行: "${line}"`)

    // 1. 匹配标准楼层格式
    const standardFloorPatterns = [
      /^(\d+)楼$/,                    // 1楼、2楼（独立行）
      /^(\d+)层$/,                    // 1层、2层（独立行）
      /^(\d+)F$/i,                    // 1F、2F（独立行）
      /^第(\d+)层$/,                  // 第1层、第2层
      /^[FB](\d+)楼?$/,               // F1楼、B1楼
    ]

    for (const pattern of standardFloorPatterns) {
      const match = line.match(pattern)
      if (match) {
        const floor = match[0]
        console.log(`   ✅ 识别标准楼层: "${floor}"`)
        return floor
      }
    }

    // 2. 匹配中文楼层
    const chineseFloorPatterns = [
      /^(一|二|三|四|五|六|七|八|九|十|十一|十二)[楼层]$/,
      /^(地下|负)(一|二|三|四|五)[楼层]$/,
      /^地下室$/,
      /^顶楼$/,
      /^阁楼$/
    ]

    for (const pattern of chineseFloorPatterns) {
      const match = line.match(pattern)
      if (match) {
        const floor = match[0]
        console.log(`   ✅ 识别中文楼层: "${floor}"`)
        return floor
      }
    }

    // 3. 匹配特殊楼层格式
    const specialFloorPatterns = [
      /(\d+)楼(?![\d])/,              // 包含楼层但不是房间号
      /(\d+)层(?![\d])/,              // 包含层数但不是房间号
      /(地下\d+层)/,                  // 地下1层、地下2层
      /(负\d+层)/,                    // 负1层、负2层
    ]

    for (const pattern of specialFloorPatterns) {
      const match = line.match(pattern)
      if (match) {
        // 确保不是房间号（如19楼01室）
        if (!line.match(/\d+楼\d+室?/) && !line.match(/\d+层\d+室?/)) {
          const floor = match[1]
          console.log(`   ✅ 识别特殊楼层: "${floor}"`)
          return floor
        }
      }
    }

    // 4. 智能楼层推断（基于上下文）
    if (i <= 2) { // 前三行更可能是楼层信息
      const contextFloorMatch = line.match(/(\d+)/)
      if (contextFloorMatch && line.length < 15) { // 放宽长度限制
        const number = parseInt(contextFloorMatch[1])
        if (number >= 1 && number <= 50) { // 合理的楼层范围
          const floor = `${number}楼`
          console.log(`   🤔 推断楼层: "${floor}" (基于上下文)`)
          return floor
        }
      }
    }

    // 5. 更宽松的楼层匹配（包含楼层关键词的行）
    if (line.includes('楼') || line.includes('层') || line.includes('F')) {
      const floorMatch = line.match(/(\d+)/)
      if (floorMatch) {
        const number = parseInt(floorMatch[1])
        if (number >= 1 && number <= 50 && !line.match(/\d+[xX×*+\-]\d+/)) { // 不是尺寸信息
          const floor = `${number}楼`
          console.log(`   🔍 宽松匹配楼层: "${floor}" (包含楼层关键词)`)
          return floor
        }
      }
    }
  }

  console.log('   ❌ 未找到楼层信息')
  return undefined
}

/**
 * 提取风口项目
 */
function extractVentItems(lines: string[]): VentItemIntent[] {
  const ventItems: VentItemIntent[] = []
  
  for (const line of lines) {
    const items = parseVentItemsFromLine(line)
    ventItems.push(...items)
  }
  
  return ventItems
}

/**
 * 从单行文本解析风口项目
 */
function parseVentItemsFromLine(line: string): VentItemIntent[] {
  const items: VentItemIntent[] = []

  console.log(`   🔍 解析行: "${line}"`)

  // 清理文本：移除序号前缀，避免干扰尺寸识别
  let cleanLine = line
  const sequenceMatch = line.match(/^(\d+)[\.\、\s]+/)
  if (sequenceMatch) {
    cleanLine = line.replace(/^(\d+)[\.\、\s]+/, '').trim()
    console.log(`     🧹 清理序号后: "${cleanLine}"`)
  }

  // 匹配尺寸模式：数字x数字（优化正则，支持更多格式和单位）
  const dimensionMatches = cleanLine.match(/(\d{1,4})\s*[xX×*+\-]\s*(\d{1,4})\s*(?:mm|㎜|毫米)?/g)

  if (!dimensionMatches) {
    console.log('     ❌ 未找到尺寸信息')
    return items
  }

  console.log(`     📏 找到 ${dimensionMatches.length} 个尺寸: ${dimensionMatches.join(', ')}`)

  for (const dimMatch of dimensionMatches) {
    // 先移除单位符号，再分割尺寸
    const cleanDimMatch = dimMatch.replace(/(?:mm|㎜|毫米)/g, '').trim()
    const dimensionParts = cleanDimMatch.split(/[xX×*+\-]/).map(s => parseInt(s.trim()))

    // 验证尺寸数据有效性
    if (dimensionParts.length !== 2 || dimensionParts.some(isNaN) || dimensionParts.some(val => val <= 0)) {
      console.log(`     ❌ 跳过无效尺寸: "${dimMatch}" -> [${dimensionParts.join(', ')}]`)
      continue
    }

    const [length, width] = dimensionParts

    if (length > 0 && width > 0) {
      console.log(`     📐 处理尺寸: ${dimMatch} (${length}x${width})`)

      // 提取数量
      const quantity = extractQuantityNearDimension(line, dimMatch)
      console.log(`     🔢 识别数量: ${quantity}`)

      // 识别风口类型
      const type = identifyVentType(line, width)
      console.log(`     🏷️ 识别类型: ${type} (基于宽度${width}mm)`)

      // 提取颜色
      const color = extractColor(line)
      if (color) console.log(`     🎨 识别颜色: ${color}`)

      // 提取备注
      const notes = extractNotes(line, dimMatch)
      if (notes) console.log(`     📝 识别备注: ${notes}`)

      // 计算置信度
      const confidence = calculateItemConfidence(line, dimMatch)
      console.log(`     📊 置信度: ${(confidence * 100).toFixed(1)}%`)

      const finalLength = Math.max(length, width) // 智能尺寸：大的作为长度
      const finalWidth = Math.min(length, width)  // 小的作为宽度

      if (finalLength !== length) {
        console.log(`     🔄 智能尺寸调整: ${length}x${width} → ${finalLength}x${finalWidth}`)
      }

      items.push({
        type,
        length: finalLength,
        width: finalWidth,
        quantity,
        color,
        notes,
        confidence,
        originalText: line
      })

      console.log(`     ✅ 风口项目创建成功: ${type} ${finalLength}x${finalWidth} x${quantity}`)
    }
  }

  return items
}

/**
 * 智能提取尺寸附近的数量信息（优化版）
 */
function extractQuantityNearDimension(line: string, dimension: string): number {
  console.log(`       🔢 智能数量识别: "${line}" 中的 "${dimension}"`)

  const dimIndex = line.indexOf(dimension)

  // 在尺寸前后查找数量
  const beforeText = line.substring(0, dimIndex)
  const afterText = line.substring(dimIndex + dimension.length)

  console.log(`       📍 前文: "${beforeText}", 后文: "${afterText}"`)

  // 查找数量模式（按优先级排序，针对手写订单优化）
  const quantityPatterns = [
    // 最可靠的格式（明确的数量词）
    { pattern: /(\d+)\s*个/, name: '个数格式', priority: 1 },
    { pattern: /(\d+)\s*只/, name: '只数格式', priority: 1 },
    { pattern: /(\d+)\s*套/, name: '套数格式', priority: 1 },

    // 较可靠的格式
    { pattern: /数量\s*[:：]\s*(\d+)/, name: '数量标记', priority: 2 },
    { pattern: /共\s*(\d+)/, name: '共计格式', priority: 2 },

    // 可能误识别的格式（降低优先级）
    { pattern: /x\s*(\d+)/i, name: 'x乘号格式', priority: 4 },
    { pattern: /×\s*(\d+)/, name: '×乘号格式', priority: 4 },

    // 单独的数字（最低优先级，容易误识别）
    { pattern: /^(\d{1,2})$/, name: '单独数字', priority: 5 },
    { pattern: /\s(\d{1,2})\s/, name: '空格包围数字', priority: 5 }
  ]

  let foundQuantities: Array<{value: number, source: string, priority: number}> = []

  // 先在后面查找（通常数量在尺寸后面）
  for (const {pattern, name, priority} of quantityPatterns) {
    const match = afterText.match(pattern)
    if (match) {
      const quantity = parseInt(match[1])
      foundQuantities.push({value: quantity, source: `后文-${name}`, priority})
      console.log(`       ✅ 在后文发现${name}: ${quantity}`)
    }
  }

  // 再在前面查找
  for (const {pattern, name, priority} of quantityPatterns) {
    const match = beforeText.match(pattern)
    if (match) {
      const quantity = parseInt(match[1])
      foundQuantities.push({value: quantity, source: `前文-${name}`, priority})
      console.log(`       ✅ 在前文发现${name}: ${quantity}`)
    }
  }

  // 查找中文数字
  const chineseNumbers = Object.keys(NUMBER_MAPPING)
  for (const num of chineseNumbers) {
    if (afterText.includes(num)) {
      const quantity = NUMBER_MAPPING[num as keyof typeof NUMBER_MAPPING]
      foundQuantities.push({value: quantity, source: `后文-中文数字`, priority: 2})
      console.log(`       ✅ 在后文发现中文数字: ${num} = ${quantity}`)
    }
    if (beforeText.includes(num)) {
      const quantity = NUMBER_MAPPING[num as keyof typeof NUMBER_MAPPING]
      foundQuantities.push({value: quantity, source: `前文-中文数字`, priority: 2})
      console.log(`       ✅ 在前文发现中文数字: ${num} = ${quantity}`)
    }
  }

  // 智能选择最合理的数量
  const finalQuantity = selectBestQuantity(foundQuantities, line)
  console.log(`       🎯 最终选择数量: ${finalQuantity}`)

  return finalQuantity
}

/**
 * 智能选择最合理的数量
 */
function selectBestQuantity(quantities: Array<{value: number, source: string, priority: number}>, originalLine: string): number {
  if (quantities.length === 0) {
    console.log(`       ⚠️ 未找到数量信息，使用默认值1`)
    return 1
  }

  // 智能过滤异常数量（根据您的观点：超过2位数很可能是错误的）
  const validQuantities = quantities.filter(q => {
    // 基本有效性检查
    if (q.value <= 0) {
      console.log(`       ❌ 过滤无效数量: ${q.value} (小于等于0)`)
      return false
    }

    // 超过2位数的数量很可能是OCR错误
    if (q.value > 99) {
      console.log(`       ❌ 过滤异常数量: ${q.value} (超过2位数，可能是识别错误)`)
      return false
    }

    // 常见OCR错误模式检测
    if (isLikelyOCRError(q.value, originalLine)) {
      console.log(`       ❌ 过滤疑似OCR错误: ${q.value}`)
      return false
    }

    return true
  })

  if (validQuantities.length === 0) {
    console.log(`       ⚠️ 所有识别的数量都异常，使用默认值1`)
    return 1
  }

  // 按优先级和合理性排序
  validQuantities.sort((a, b) => {
    // 优先级越小越优先
    if (a.priority !== b.priority) {
      return a.priority - b.priority
    }

    // 相同优先级下，选择更合理的数量
    // 1-20是最常见的风口数量范围
    const aReasonable = a.value >= 1 && a.value <= 20
    const bReasonable = b.value >= 1 && b.value <= 20

    if (aReasonable && !bReasonable) return -1
    if (!aReasonable && bReasonable) return 1

    // 都合理或都不合理时，选择较小的
    return a.value - b.value
  })

  // 如果没有找到合理的数量，尝试智能推断
  if (validQuantities.length === 0) {
    const inferredQuantity = inferQuantityFromContext(originalLine)
    if (inferredQuantity > 0) {
      console.log(`       🧠 智能推断数量: ${inferredQuantity}`)
      return inferredQuantity
    }
  }

  const selected = validQuantities[0]
  console.log(`       ✅ 选择最佳数量: ${selected.value} (来源: ${selected.source})`)

  // 如果选择的数量仍然可疑，添加警告
  if (selected.value > 50) {
    console.log(`       ⚠️ 数量${selected.value}较大，建议人工确认`)
  }

  return selected.value
}

/**
 * 检测是否为可能的OCR错误
 */
function isLikelyOCRError(quantity: number, originalLine: string): boolean {
  // 常见的OCR错误模式
  const errorPatterns = [
    // 数字442很可能是"4个"的误识别
    { value: 442, likely: '4个', reason: '442很可能是"4个"的OCR错误' },
    { value: 422, likely: '4个', reason: '422很可能是"4个"的OCR错误' },
    { value: 411, likely: '4个', reason: '411很可能是"4个"的OCR错误' },

    // 其他常见错误
    { value: 100, likely: '1个', reason: '100可能是"1个"的OCR错误' },
    { value: 200, likely: '2个', reason: '200可能是"2个"的OCR错误' },
    { value: 300, likely: '3个', reason: '300可能是"3个"的OCR错误' },
    { value: 500, likely: '5个', reason: '500可能是"5个"的OCR错误' },

    // 尺寸误识别为数量
    { value: 140, likely: '尺寸', reason: '140可能是尺寸而非数量' },
    { value: 160, likely: '尺寸', reason: '160可能是尺寸而非数量' },
    { value: 300, likely: '尺寸', reason: '300可能是尺寸而非数量' }
  ]

  for (const pattern of errorPatterns) {
    if (quantity === pattern.value) {
      console.log(`       ⚠️ ${pattern.reason}`)
      return true
    }
  }

  // 检查是否包含尺寸数字（可能被误识别为数量）
  const dimensionNumbers = originalLine.match(/\d+/g) || []
  for (const dimStr of dimensionNumbers) {
    const dimNum = parseInt(dimStr)
    if (dimNum === quantity && dimNum > 50) {
      console.log(`       ⚠️ 数量${quantity}与尺寸数字重复，可能是误识别`)
      return true
    }
  }

  // 异常大的数量（50以上）需要特别检查
  if (quantity > 50) {
    console.log(`       ⚠️ 数量${quantity}异常大，可能是OCR错误`)
    return true
  }

  return false
}

/**
 * 从上下文智能推断数量
 */
function inferQuantityFromContext(line: string): number {
  console.log(`       🧠 尝试从上下文推断数量: "${line}"`)

  // 如果包含"各"、"每"等词，通常表示1个
  if (line.includes('各') || line.includes('每')) {
    console.log(`       ✅ 发现"各"或"每"，推断为1个`)
    return 1
  }

  // 如果是房间描述（如"客厅"、"卧室"），通常是1个
  const roomKeywords = ['客厅', '卧室', '厨房', '卫生间', '书房', '餐厅', '阳台']
  for (const room of roomKeywords) {
    if (line.includes(room)) {
      console.log(`       ✅ 发现房间关键词"${room}"，推断为1个`)
      return 1
    }
  }

  // 如果包含"一套"、"一批"等，推断为1
  if (line.includes('一套') || line.includes('一批') || line.includes('一组')) {
    console.log(`       ✅ 发现"一套/一批/一组"，推断为1个`)
    return 1
  }

  // 检查是否有被误识别的数量（如442可能是4）
  const suspiciousNumbers = line.match(/\d{3,}/g) || []
  for (const numStr of suspiciousNumbers) {
    const num = parseInt(numStr)
    if (num >= 100 && num <= 999) {
      // 尝试提取第一位数字作为真实数量
      const firstDigit = Math.floor(num / 100)
      if (firstDigit >= 1 && firstDigit <= 9) {
        console.log(`       ✅ 从疑似错误数字${num}推断真实数量为${firstDigit}`)
        return firstDigit
      }
    }
  }

  console.log(`       ❌ 无法从上下文推断数量`)
  return 0
}

/**
 * 识别风口类型（优化版：更准确的判断逻辑）
 */
function identifyVentType(line: string, width: number): string {
  console.log(`       🔍 风口类型识别: 文本="${line}", 宽度=${width}mm`)

  // 基于关键词识别
  for (const [keyword, type] of Object.entries(VENT_TYPE_MAPPING)) {
    if (line.includes(keyword)) {
      console.log(`       ✅ 关键词匹配: "${keyword}" → ${type}`)
      return type
    }
  }

  console.log('       ⚠️ 未找到关键词匹配，使用宽度智能判断')

  // 优化的宽度判断逻辑（基于实际风口规格）
  if (width >= 255) {
    console.log(`       📏 宽度判断: ${width}mm ≥ 255mm → 回风口`)
    return 'white_return' // 255mm及以上通常是回风口
  } else if (width <= 160) {
    console.log(`       📏 宽度判断: ${width}mm ≤ 160mm → 线型风口`)
    return 'white_linear' // 160mm及以下是线型风口
  } else {
    console.log(`       📏 宽度判断: ${width}mm 在160-255mm之间 → 出风口`)
    return 'double_white_outlet' // 160-255mm之间是出风口
  }
}

/**
 * 提取颜色信息
 */
function extractColor(line: string): string | undefined {
  for (const [keyword, color] of Object.entries(COLOR_MAPPING)) {
    if (line.includes(keyword)) {
      return color
    }
  }
  return undefined
}

/**
 * 提取备注信息（增强版：融合房间名称和尺寸后描述）
 */
function extractNotes(line: string, dimension: string): string | undefined {
  console.log(`       🔍 提取备注信息: "${line}"`)

  const allNotes: string[] = []

  // 1. 提取房间名称
  const roomNotes = extractRoomNames(line)
  if (roomNotes) {
    allNotes.push(roomNotes)
    console.log(`       🏠 识别房间名称: "${roomNotes}"`)
  }

  // 2. 总是尝试提取尺寸后面的描述性文字（不管是否有房间名称）
  const dimensionAfterNotes = extractNotesAfterDimension(line, dimension)
  if (dimensionAfterNotes) {
    allNotes.push(dimensionAfterNotes)
    console.log(`       📝 识别尺寸后描述: "${dimensionAfterNotes}"`)
  }

  // 3. 智能提取技术规格备注
  const techNotes = extractTechnicalNotes(line, dimension)
  if (techNotes) {
    allNotes.push(techNotes)
    console.log(`       🔧 识别技术规格: "${techNotes}"`)
  }

  // 4. 提取位置相关备注
  const locationNotes = extractLocationNotes(line)
  if (locationNotes) {
    allNotes.push(locationNotes)
    console.log(`       📍 识别位置信息: "${locationNotes}"`)
  }

  // 5. 如果没有找到任何特定备注，使用原有的通用备注提取逻辑
  if (allNotes.length === 0) {
    const generalNotes = extractGeneralNotes(line, dimension)
    if (generalNotes) {
      allNotes.push(generalNotes)
      console.log(`       📄 识别通用备注: "${generalNotes}"`)
    }
  }

  // 6. 合并所有备注并去重
  if (allNotes.length > 0) {
    const uniqueNotes = [...new Set(allNotes)] // 去重
    const finalNotes = uniqueNotes.join(' ').trim()
    console.log(`       ✅ 最终备注: "${finalNotes}"`)
    return finalNotes
  }

  console.log(`       ❌ 未找到有效备注`)
  return undefined
}

/**
 * 提取尺寸后面的描述性文字（优化版）
 */
function extractNotesAfterDimension(line: string, dimension: string): string | undefined {
  if (!dimension) return undefined

  const dimensionIndex = line.indexOf(dimension)
  if (dimensionIndex === -1) return undefined

  console.log(`       📝 尺寸后描述提取: "${line}" 中 "${dimension}" 后的内容`)

  // 获取尺寸后面的文字
  const afterDimension = line.substring(dimensionIndex + dimension.length).trim()
  if (!afterDimension) return undefined

  console.log(`       📍 尺寸后原始内容: "${afterDimension}"`)

  // 移除数量信息和无用符号，保留描述性文字
  let notes = afterDimension
    .replace(/^\s*[，,。；;=\+\-\*\/]\s*/, '') // 移除开头的标点和运算符
    .replace(/\d+\s*[个只套件]\s*[，,。；;]?\s*/, '') // 移除数量信息
    .replace(/数量\s*[:：=]\s*\d+\s*[，,。；;]?\s*/, '') // 移除数量标签
    .replace(/mm|㎜|毫米/g, '') // 移除单位
    .replace(/\s*[=\+\-\*\/]\s*/g, ' ') // 运算符转空格
    .replace(/\s+/g, ' ') // 多空格合并
    .trim()

  console.log(`       🧹 清理后内容: "${notes}"`)

  // 更宽松的判断条件
  if (notes.length > 0 && notes.length < 100 && // 增加长度限制
      !/^\d+$/.test(notes) && // 不是纯数字
      !/^[，,。；;=\+\-\*\/\s]+$/.test(notes) && // 不是纯符号
      !notes.includes('风口') && // 不包含风口关键词
      !/^\d+[xX×*+\-]\d+/.test(notes) && // 不是尺寸格式
      notes.length >= 2) { // 至少2个字符

    console.log(`       ✅ 提取到尺寸后描述: "${notes}"`)
    return notes
  }

  console.log(`       ❌ 尺寸后描述不符合条件`)
  return undefined
}

/**
 * 提取通用备注信息（优化版：更智能的过滤）
 */
function extractGeneralNotes(line: string, dimension: string): string | undefined {
  console.log(`       🔍 通用备注提取: "${line}"`)

  // 移除尺寸和数量信息，提取其他备注
  let notes = line
    .replace(dimension, '') // 移除尺寸
    .replace(/\d+\s*[个只套件]/g, '') // 移除数量
    .replace(/数量\s*[:：=]\s*\d+/g, '') // 移除数量标记
    .replace(/[出回]风口?/g, '') // 移除风口类型
    .replace(/[:：=]/g, '') // 移除分隔符
    .replace(/mm|㎜|毫米/g, '') // 移除单位
    .trim()

  // 清理无意义的符号和空格
  notes = notes
    .replace(/[，,。；;]/g, ' ') // 标点转空格
    .replace(/\s+/g, ' ') // 多空格合并
    .replace(/^\s*[\-\+\*\/=]\s*/, '') // 移除开头的运算符
    .replace(/\s*[\-\+\*\/=]\s*$/, '') // 移除结尾的运算符
    .trim()

  console.log(`       🧹 清理后备注: "${notes}"`)

  // 更宽松的过滤条件
  if (notes.length > 0 && notes.length < 50 && // 增加长度限制
      !/^\d+[xX×*+\-]\d+/.test(notes) && // 不是尺寸格式
      !/^\d+✖️\d+/.test(notes) && // 不是✖️格式的尺寸
      !/^\d+$/.test(notes) && // 不是纯数字
      !/^[=\+\-\*\/]+$/.test(notes) && // 不是纯运算符
      notes !== 'mm' && notes !== '㎜' && notes !== '毫米' && // 不是单位
      notes.length >= 2) { // 至少2个字符

    console.log(`       ✅ 提取到通用备注: "${notes}"`)
    return notes
  }

  console.log(`       ❌ 备注不符合条件`)
  return undefined
}

/**
 * 提取技术规格备注
 */
function extractTechnicalNotes(line: string, dimension: string): string | undefined {
  const techPatterns = [
    // 出风方式
    /普通下出/g,
    /侧出/g,
    /上出/g,
    /下出/g,
    /四面出风/g,
    /双向出风/g,

    // 连接方式
    /留圆接\d*软管/g,
    /留圆接\d*/g,
    /软管连接/g,
    /硬管连接/g,
    /法兰连接/g,

    // 材质规格
    /铝合金/g,
    /不锈钢/g,
    /塑料/g,
    /ABS/g,

    // 特殊要求
    /防火/g,
    /防水/g,
    /静音/g,
    /可调/g,
    /固定/g
  ]

  const foundTech: string[] = []

  for (const pattern of techPatterns) {
    const matches = line.match(pattern)
    if (matches) {
      foundTech.push(...matches)
    }
  }

  return foundTech.length > 0 ? foundTech.join(' ') : undefined
}

/**
 * 提取位置相关备注
 */
function extractLocationNotes(line: string): string | undefined {
  const locationPatterns = [
    // 方位
    /东侧/g, /西侧/g, /南侧/g, /北侧/g,
    /左侧/g, /右侧/g, /中间/g, /角落/g,

    // 高度位置
    /吊顶/g, /墙面/g, /地面/g, /顶部/g, /底部/g,

    // 相对位置
    /靠窗/g, /靠门/g, /靠墙/g, /中央/g,

    // 楼层位置增强
    /地下室/g, /一楼/g, /二楼/g, /三楼/g, /四楼/g, /五楼/g,
    /顶楼/g, /阁楼/g, /夹层/g
  ]

  const foundLocation: string[] = []

  for (const pattern of locationPatterns) {
    const matches = line.match(pattern)
    if (matches) {
      foundLocation.push(...matches)
    }
  }

  return foundLocation.length > 0 ? foundLocation.join(' ') : undefined
}

/**
 * 提取房间名称作为备注（增强版）
 */
function extractRoomNames(line: string): string | undefined {
  // 定义房间关键词（扩展版：包含异体字和变体）
  const roomKeywords = [
    // 基础房间（包含异体字和常见错误识别）
    '大厅', '客厅', '客广', '餐厅', '歺厅', '厨房', '卫生间', '卫生问', '洗手间', '厕所',
    '阳台', '书房', '书房间', '储物间', '储藏室', '玄关', '过道', '走廊',

    // 卧室相关
    '主卧', '主卧室', '次卧', '次卧室', '卧室', '儿童房', '老人房',

    // 房间编号（扩展更多数字）
    '小房间', '小房', '房间', '房',
    '小房1', '小房2', '小房3', '小房4', '小房5', '小房6', '小房7', '小房8',
    '房间1', '房间2', '房间3', '房间4', '房间5', '房间6', '房间7', '房间8',
    '1号房', '2号房', '3号房', '4号房', '5号房', '6号房', '7号房', '8号房',

    // 功能区域（扩展）
    '办公室', '会议室', '接待室', '休息室', '活动室', '娱乐室',
    '衣帽间', '更衣室', '洗衣房', '杂物间', '设备间', '机房',
    '茶室', '茶房', '棋牌室', '麻将室', '游戏室', '影音室',
    'KTV', 'ktv', 'K歌房', '包房', '唱歌房',

    // 商业空间
    '大堂', '前台', '接待区', '等候区', '休息区', '包间', '包厢',

    // 住宅特殊区域
    '检修口', '维修口', '设备间', '配电间', '弱电间', '强电间',
    '储藏间', '杂物房', '工具间', '清洁间'
  ]

  const foundRooms: string[] = []

  // 查找所有匹配的房间名称
  for (const keyword of roomKeywords) {
    if (line.includes(keyword)) {
      foundRooms.push(keyword)
      console.log(`       🏠 发现房间关键词: "${keyword}"`)
    }
  }

  // 如果找到房间名称，返回最长的那个（更具体）
  if (foundRooms.length > 0) {
    const longestRoom = foundRooms.reduce((a, b) => a.length > b.length ? a : b)
    return longestRoom
  }

  // 尝试提取字母+数字房间编号（如"A201", "B302", "C1001"）
  const alphaNumericRoomMatch = line.match(/([A-Z]\d{2,4})/g)
  if (alphaNumericRoomMatch) {
    console.log(`       🏠 发现字母数字房间编号: "${alphaNumericRoomMatch[0]}"`)
    return alphaNumericRoomMatch[0]
  }

  // 尝试提取数字+房间的模式（如"1901号房"、"1901号房661901"）
  const roomNumberMatch = line.match(/(\d+号?房\d*)/g)
  if (roomNumberMatch) {
    // 如果匹配到多个，选择最短的（避免包含额外数字）
    const bestMatch = roomNumberMatch.reduce((a, b) => a.length <= b.length ? a : b)
    console.log(`       🏠 发现房间编号: "${bestMatch}"`)
    return bestMatch
  }

  // 尝试提取楼层+房间的模式（如"19楼01室"）
  const floorRoomMatch = line.match(/(\d+楼\d+室?)/g)
  if (floorRoomMatch) {
    console.log(`       🏠 发现楼层房间: "${floorRoomMatch[0]}"`)
    return floorRoomMatch[0]
  }

  return undefined
}

/**
 * 计算单项置信度
 */
function calculateItemConfidence(line: string, dimension: string): number {
  let confidence = 0.5 // 基础置信度
  
  // 有明确的风口类型关键词
  if (Object.keys(VENT_TYPE_MAPPING).some(keyword => line.includes(keyword))) {
    confidence += 0.3
  }
  
  // 有数量信息
  if (/\d+\s*[个只套]/.test(line) || /数量/.test(line)) {
    confidence += 0.2
  }
  
  // 尺寸格式标准
  if (/\d+\s*[xX×]\s*\d+/.test(dimension)) {
    confidence += 0.1
  }
  
  return Math.min(confidence, 1.0)
}

/**
 * 计算整体置信度
 */
function calculateOverallConfidence(ventItems: VentItemIntent[]): number {
  if (ventItems.length === 0) return 0

  const avgConfidence = ventItems.reduce((sum, item) => sum + item.confidence, 0) / ventItems.length

  // 项目数量奖励
  const countBonus = Math.min(ventItems.length * 0.1, 0.3)

  return Math.min(avgConfidence + countBonus, 1.0)
}

/**
 * 使用房间上下文增强风口项目（智能关联同房间的出风口和回风口）
 */
export function enhanceWithRoomContext(items: VentItemIntent[], originalText: string): VentItemIntent[] {
  console.log('🏠 开始房间上下文增强...')

  const lines = originalText.split('\n').filter(line => line.trim())
  const enhancedItems: VentItemIntent[] = []
  let currentRoom: string | undefined = undefined

  // 逐行分析，建立房间上下文
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()

    // 检查是否包含风口信息（支持多种尺寸格式）
    const hasVentInfo = line.includes('风口') && /\d+\s*[xX×*]\s*\d+/i.test(line)
    const hasVentDimension = /\d+✖️\d+/.test(line)
    const actualHasVentInfo = hasVentInfo || hasVentDimension

    if (actualHasVentInfo) {
      // 提取房间名称
      const roomName = extractRoomNames(line)

      if (roomName) {
        // 如果当前行有房间名称，更新当前房间
        currentRoom = roomName
        console.log(`   🏠 发现新房间: ${currentRoom}`)
      }

      // 查找对应的风口项目
      const matchingItem = findMatchingVentItem(items, line)
      if (matchingItem) {
        // 创建增强的项目副本
        const enhancedItem = { ...matchingItem }

        // 如果项目没有备注但有当前房间上下文，添加房间信息
        if (!enhancedItem.notes && currentRoom) {
          enhancedItem.notes = currentRoom
          console.log(`   ✅ 为风口添加房间信息: ${enhancedItem.type} -> ${currentRoom}`)
        } else if (enhancedItem.notes && currentRoom && !enhancedItem.notes.includes(currentRoom)) {
          // 如果已有备注但不包含房间信息，则合并
          enhancedItem.notes = `${currentRoom} ${enhancedItem.notes}`
          console.log(`   ✅ 合并房间信息到现有备注: ${enhancedItem.notes}`)
        }

        enhancedItems.push(enhancedItem)
      }
    } else {
      // 非风口行，检查是否包含房间信息（用于下一个风口）
      const roomName = extractRoomNames(line)
      if (roomName) {
        currentRoom = roomName
        console.log(`   🏠 发现房间上下文: ${currentRoom}`)
      }
    }
  }

  // 如果没有找到匹配的项目，返回原始项目
  if (enhancedItems.length === 0) {
    console.log('   ⚠️ 未找到匹配项目，返回原始结果')
    return items
  }

  console.log(`   ✅ 房间上下文增强完成，处理了 ${enhancedItems.length} 个项目`)
  return enhancedItems
}

/**
 * 查找匹配的风口项目
 */
function findMatchingVentItem(items: VentItemIntent[], line: string): VentItemIntent | undefined {
  // 提取行中的尺寸信息
  const dimensionMatch = line.match(/(\d+)\s*[xX×*]\s*(\d+)/i)
  if (!dimensionMatch) return undefined

  const length1 = parseInt(dimensionMatch[1])
  const length2 = parseInt(dimensionMatch[2])

  // 智能尺寸排序（长度 >= 宽度）
  const targetLength = Math.max(length1, length2)
  const targetWidth = Math.min(length1, length2)

  // 判断风口类型
  const isReturn = line.includes('回风')

  // 查找匹配的项目
  for (const item of items) {
    const lengthMatch = item.length === targetLength && item.width === targetWidth
    const typeMatch = (isReturn && item.type.includes('return')) ||
                     (!isReturn && !item.type.includes('return'))

    if (lengthMatch && typeMatch) {
      return item
    }
  }

  return undefined
}

/**
 * 从表格中提取项目名称
 */
function extractProjectNameFromTable(tableLayout: TableLayout): string | undefined {
  // 查找标题行或第一行的项目信息
  if (tableLayout.rows.length === 0) return undefined

  const firstRow = tableLayout.rows[0]
  const titleCells = firstRow.cells.filter(cell =>
    cell.text.includes('项目') ||
    cell.text.includes('工程') ||
    cell.text.length > 10
  )

  return titleCells.length > 0 ? titleCells[0].text : undefined
}

/**
 * 从表格中提取楼层信息
 */
function extractFloorInfoFromTable(tableLayout: TableLayout): string | undefined {
  const floorColumn = tableLayout.columns.find(col => col.type === 'floor')
  if (!floorColumn || floorColumn.cells.length < 2) return undefined

  // 获取第一个数据行的楼层信息
  const dataCell = floorColumn.cells.find(cell => cell.row > 0)
  return dataCell?.text
}

/**
 * 从表格中提取风口项目（增强版）
 */
function extractVentItemsFromTable(tableLayout: TableLayout): VentItemIntent[] {
  console.log('     🔍 基于表格布局提取风口项目（增强版）...')
  console.log(`     📊 表格信息: ${tableLayout.columns.length} 列, ${tableLayout.rows.length} 行`)

  const ventItems: VentItemIntent[] = []

  // 获取各列并打印调试信息
  const dimensionColumn = tableLayout.columns.find(col => col.type === 'dimension')
  const productColumn = tableLayout.columns.find(col => col.type === 'product')
  const quantityColumn = tableLayout.columns.find(col => col.type === 'quantity')
  const noteColumn = tableLayout.columns.find(col => col.type === 'note')
  const floorColumn = tableLayout.columns.find(col => col.type === 'floor')
  const locationColumn = tableLayout.columns.find(col => col.type === 'location')

  console.log('     📋 列识别结果:')
  tableLayout.columns.forEach((col, index) => {
    console.log(`       列${index + 1}: "${col.name}" → ${col.type} (${col.cells.length} 个单元格)`)
  })

  // 如果没有找到尺寸列，尝试从所有列中查找包含尺寸的列
  if (!dimensionColumn) {
    console.log('     🔍 未找到专门的尺寸列，尝试从所有列中查找尺寸信息...')

    // 查找包含尺寸格式的列
    for (const column of tableLayout.columns) {
      const hasSizeData = column.cells.some(cell =>
        cell.text && /\d+\s*[x×*+\-]\s*\d+/.test(cell.text)
      )
      if (hasSizeData) {
        console.log(`     ✅ 在列"${column.name}"中找到尺寸数据`)
        // 临时将此列标记为尺寸列
        column.type = 'dimension'
        break
      }
    }
  }

  const finalDimensionColumn = tableLayout.columns.find(col => col.type === 'dimension')
  if (!finalDimensionColumn) {
    console.log('     ❌ 仍未找到尺寸信息，无法提取风口项目')
    return ventItems
  }

  // 遍历数据行（跳过表头）
  const dataRows = tableLayout.rows.filter(row => row.type === 'data')
  console.log(`     📊 找到 ${dataRows.length} 行数据`)

  // 如果没有明确的数据行，尝试处理所有非表头行
  if (dataRows.length === 0) {
    console.log(`     🔄 没有明确的数据行，尝试处理所有行...`)
    const allRows = tableLayout.rows.filter((row, index) => index > 0) // 跳过第一行（表头）
    allRows.forEach((row, index) => processTableRow(row, index, finalDimensionColumn, productColumn, quantityColumn, noteColumn, floorColumn, locationColumn, ventItems))
  } else {
    dataRows.forEach((row, index) => processTableRow(row, index, finalDimensionColumn, productColumn, quantityColumn, noteColumn, floorColumn, locationColumn, ventItems))
  }

  return ventItems
}

/**
 * 处理单个表格行
 */
function processTableRow(
  row: any,
  index: number,
  dimensionColumn: any,
  productColumn: any,
  quantityColumn: any,
  noteColumn: any,
  floorColumn: any,
  locationColumn: any,
  ventItems: VentItemIntent[]
) {
  console.log(`     📦 处理第${index + 1}行数据...`)

  // 获取尺寸信息 - 改进的尺寸提取逻辑
  const dimensionCell = dimensionColumn?.cells.find(cell => cell.row === row.cells[0].row)
  let dimensionText = ''

  if (dimensionCell && dimensionCell.text.trim()) {
    dimensionText = dimensionCell.text.trim()
  } else {
    // 如果没有专门的尺寸列，尝试从所有单元格中查找尺寸
    for (const cell of row.cells) {
      if (cell.text && /\d+\s*[x×*+\-]\s*\d+/.test(cell.text)) {
        dimensionText = cell.text.trim()
        console.log(`     🔍 从其他列找到尺寸: "${dimensionText}"`)
        break
      }
    }
  }

  if (!dimensionText) {
    console.log(`     ❌ 第${index + 1}行无尺寸信息，跳过`)
    return
  }

  // 改进的尺寸匹配正则表达式
  const dimensionMatch = dimensionText.match(/(\d+)\s*[x×*+\-]\s*(\d+)/)

  if (!dimensionMatch) {
    console.log(`     ❌ 第${index + 1}行尺寸格式无效: "${dimensionText}"`)
    return
  }

  const [, length, width] = dimensionMatch
  const finalLength = Math.max(parseInt(length), parseInt(width))
  const finalWidth = Math.min(parseInt(length), parseInt(width))

  // 获取产品类型 - 改进的产品类型识别
  const productCell = productColumn?.cells.find(cell => cell.row === row.cells[0].row)
  let productText = productCell?.text || ''

  // 如果没有专门的产品列，尝试从所有单元格中查找产品信息
  if (!productText) {
    for (const cell of row.cells) {
      if (cell.text && (
        cell.text.includes('风口') ||
        cell.text.includes('回风') ||
        cell.text.includes('出风') ||
        cell.text.includes('线型') ||
        cell.text.includes('检修')
      )) {
        productText = cell.text
        console.log(`     🔍 从其他列找到产品信息: "${productText}"`)
        break
      }
    }
  }

  const type = identifyVentTypeFromProduct(productText, finalWidth)

  // 获取数量 - 改进的数量提取
  const quantityCell = quantityColumn?.cells.find(cell => cell.row === row.cells[0].row)
  let quantityText = quantityCell?.text || '1'

  // 如果没有专门的数量列，尝试从尺寸文本中提取数量
  if (!quantityText || quantityText === '1') {
    const quantityInDimension = dimensionText.match(/(\d+)\s*[个只套件台]/)
    if (quantityInDimension) {
      quantityText = quantityInDimension[1]
      console.log(`     🔍 从尺寸文本中找到数量: ${quantityText}`)
    }
  }

  const quantity = extractQuantityFromCell(quantityText)

  // 获取干净的备注信息 - 改进的备注清理
  const cleanNotes = extractCleanNotesFromTableEnhanced(row, {
    floor: floorColumn?.cells.find(cell => cell.row === row.cells[0].row)?.text,
    location: locationColumn?.cells.find(cell => cell.row === row.cells[0].row)?.text,
    note: noteColumn?.cells.find(cell => cell.row === row.cells[0].row)?.text,
    allCells: row.cells.map((cell: any) => cell.text).join(' ')
  })

  const ventItem: VentItemIntent = {
    type,
    length: finalLength,
    width: finalWidth,
    quantity,
    notes: cleanNotes,
    confidence: 0.9, // 表格提取的置信度较高
    originalText: row.cells.map((cell: any) => cell.text).join(' ')
  }

  ventItems.push(ventItem)

  const notesDisplay = cleanNotes ? ` (备注: ${cleanNotes})` : ''
  console.log(`       ✅ 创建风口: ${type} ${finalLength}x${finalWidth} x${quantity}${notesDisplay}`)
}

/**
 * 基于产品信息识别风口类型（增强版）
 */
function identifyVentTypeFromProduct(productText: string, width: number): string {
  console.log(`       🔍 产品类型识别: "${productText}", 宽度=${width}mm`)

  const text = productText.toLowerCase()

  // 1. 优先级最高：检修口/维修口
  if (text.includes('检修') || text.includes('维修')) {
    console.log(`       ✅ 检修口识别`)
    return 'maintenance'
  }

  // 2. 特殊类型识别
  if (text.includes('箭型') || text.includes('箭形') || text.includes('t型')) {
    const isReturn = text.includes('回风') || width >= 255
    console.log(`       ✅ 箭型风口识别: ${isReturn ? '回风' : '出风'}`)
    return isReturn ? 'arrow_return' : 'arrow_outlet'
  }

  if (text.includes('爪型') || text.includes('爪形')) {
    const isReturn = text.includes('回风') || width >= 300
    console.log(`       ✅ 爪型风口识别: ${isReturn ? '回风' : '出风'}`)
    return isReturn ? 'claw_return' : 'claw_outlet'
  }

  // 3. 颜色识别
  const isBlack = text.includes('黑') || text.includes('黑色')

  // 4. 基本类型识别
  if (text.includes('回风') || text.includes('进气')) {
    console.log(`       ✅ 回风口识别: ${isBlack ? '黑色' : '白色'}`)
    return isBlack ? 'black_return' : 'white_return'
  }

  if (text.includes('出风') || text.includes('送风')) {
    console.log(`       ✅ 出风口识别: ${isBlack ? '黑色' : '白色'}`)
    return isBlack ? 'double_black_outlet' : 'double_white_outlet'
  }

  if (text.includes('线型') || text.includes('线形') || text.includes('条形') || text.includes('条型')) {
    const isReturn = text.includes('回风') || text.includes('进气')
    console.log(`       ✅ 线型风口识别: ${isReturn ? '回风' : '出风'}, ${isBlack ? '黑色' : '白色'}`)
    if (isReturn) {
      return isBlack ? 'black_linear_return' : 'white_linear_return'
    } else {
      return isBlack ? 'black_linear' : 'white_linear'
    }
  }

  // 5. 基于宽度的智能判断（改进版）
  console.log(`       🤖 基于宽度智能判断: ${width}mm`)

  if (width >= 255) {
    console.log(`       📏 宽度≥255mm → 回风口`)
    return isBlack ? 'black_return' : 'white_return'
  } else if (width <= 150) {
    console.log(`       📏 宽度≤150mm → 线型风口`)
    return isBlack ? 'black_linear' : 'white_linear'
  } else {
    console.log(`       📏 宽度150-255mm → 出风口`)
    return isBlack ? 'double_black_outlet' : 'double_white_outlet'
  }
}

/**
 * 从单元格文本中提取数量
 */
function extractQuantityFromCell(cellText: string): number {
  const quantityMatch = cellText.match(/(\d+)\s*[个只套件]?/)
  return quantityMatch ? parseInt(quantityMatch[1]) : 1
}

/**
 * 从表格中提取干净的备注信息（增强版）
 */
function extractCleanNotesFromTableEnhanced(row: any, rowData: {
  floor?: string
  location?: string
  note?: string
  allCells?: string
}): string | undefined {
  console.log(`       📝 提取干净备注（增强版）...`)

  const notes: string[] = []
  const excludePatterns = [
    /序号.*次/,           // 排除"序号二次"等
    /^\d+$/,              // 排除纯数字
    /^\d+[x×*+\-]\d+$/,   // 排除纯尺寸
    /^[￥¥]\d+/,          // 排除价格
    /^\d+\.?\d*mm$/,      // 排除单位尺寸
    /^黑色$/,             // 排除单独的颜色词
    /^白色$/,
    /^出风口$/,           // 排除单独的类型词
    /^回风口$/,
    /^风口$/
  ]

  // 添加楼层信息（如果有且有意义）
  if (rowData.floor && rowData.floor.trim()) {
    const floorText = rowData.floor.trim()
    if (/[楼层F]/.test(floorText) &&
        !floorText.includes('mm') &&
        !floorText.includes('￥') &&
        !excludePatterns.some(pattern => pattern.test(floorText))) {
      notes.push(floorText)
    }
  }

  // 添加位置信息（如果有且有意义）
  if (rowData.location && rowData.location.trim()) {
    const locationText = rowData.location.trim()
    if (locationText.length > 1 &&
        !locationText.includes('mm') &&
        !locationText.includes('￥') &&
        !excludePatterns.some(pattern => pattern.test(locationText))) {
      notes.push(locationText)
    }
  }

  // 添加备注信息（清理后）
  if (rowData.note && rowData.note.trim()) {
    let cleanNote = rowData.note
      .replace(/序号.*次/g, '')        // 移除"序号二次"等
      .replace(/mm|㎜|毫米/g, '')      // 移除单位
      .replace(/￥\d+(\.\d+)?/g, '')   // 移除价格
      .replace(/¥\d+(\.\d+)?/g, '')    // 移除价格
      .replace(/\d+\s*[个只套件]/g, '') // 移除数量
      .replace(/^\d+\s*/, '')          // 移除开头数字（序号）
      .replace(/出风口小线形/g, '出风口') // 修正常见错误
      .replace(/回风口小线形/g, '回风口') // 修正常见错误
      .replace(/\s+/g, ' ')            // 合并空格
      .trim()

    if (cleanNote.length > 1 &&
        !excludePatterns.some(pattern => pattern.test(cleanNote))) {
      notes.push(cleanNote)
    }
  }

  // 从所有单元格中提取有用信息（如果前面没有找到足够信息）
  if (notes.length === 0 && rowData.allCells) {
    const allText = rowData.allCells
    const meaningfulParts = allText.split(/\s+/).filter(part => {
      return part.length > 1 &&
             !excludePatterns.some(pattern => pattern.test(part)) &&
             !/^\d+[x×*+\-]\d+$/.test(part) && // 不是尺寸
             !/^\d+$/.test(part) &&            // 不是纯数字
             !part.includes('￥') &&           // 不是价格
             part !== '黑色' && part !== '白色' // 不是单独的颜色
    })

    if (meaningfulParts.length > 0) {
      notes.push(meaningfulParts.join(' '))
    }
  }

  const result = notes.join(' ').trim()
  console.log(`       📝 提取到干净备注: "${result}"`)
  return result || undefined
}
