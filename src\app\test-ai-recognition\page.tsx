/**
 * 🇨🇳 风口云平台 - AI识别测试页面
 */

'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { getQwenAPI } from '@/lib/services/qwen-api'

const API_KEYS = {
  QWEN: process.env.NEXT_PUBLIC_QWEN_API_KEY || 'sk-b8b8c8e4c8f44f5b9b8b8c8e4c8f44f5b'
}

export default function TestAIRecognitionPage() {
  const [inputText, setInputText] = useState('回风200x1600，出风口2640X150，回风口2640X300.回风口2000X300，出风口2000X145，回风口4840X300.出风口2470X150，回风口2470X300，回风口1600X300，1600x145，出风口2000X150，回风口1600X300')
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const testRecognition = async () => {
    if (!inputText.trim()) {
      setError('请输入测试内容')
      return
    }

    setLoading(true)
    setError('')
    setResult(null)

    try {
      console.log('🚀 开始AI识别测试...')
      const qwen = getQwenAPI(API_KEYS.QWEN)
      const analysisResult = await qwen.analyzeVentOrder(inputText)
      
      console.log('✅ AI识别完成:', analysisResult)
      setResult(analysisResult)
    } catch (err) {
      console.error('❌ AI识别失败:', err)
      setError(err instanceof Error ? err.message : '识别失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen p-8 bg-gray-50">
      <div className="max-w-6xl mx-auto space-y-6">
        <h1 className="text-3xl font-bold text-center">🤖 AI识别测试页面</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 输入区域 */}
          <Card>
            <CardHeader>
              <CardTitle>测试输入</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Textarea
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                placeholder="输入要识别的风口信息..."
                rows={10}
                className="font-mono text-sm"
              />
              
              <Button 
                onClick={testRecognition}
                disabled={loading}
                className="w-full"
              >
                {loading ? '识别中...' : '🚀 开始AI识别'}
              </Button>
              
              {error && (
                <div className="p-3 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                  ❌ {error}
                </div>
              )}
            </CardContent>
          </Card>

          {/* 结果区域 */}
          <Card>
            <CardHeader>
              <CardTitle>识别结果</CardTitle>
            </CardHeader>
            <CardContent>
              {result ? (
                <div className="space-y-4">
                  <div className="p-3 bg-green-50 border border-green-200 rounded">
                    <p className="text-green-700 font-medium">✅ 识别成功</p>
                  </div>
                  
                  {result.projects && result.projects.map((project: any, index: number) => (
                    <div key={index} className="border rounded p-4 space-y-3">
                      <h3 className="font-bold text-lg">项目 {index + 1}</h3>
                      <p><strong>项目名称:</strong> {project.projectName}</p>
                      <p><strong>客户信息:</strong> {project.clientInfo}</p>

                      {project.floors && project.floors.map((floor: any, floorIndex: number) => (
                        <div key={floorIndex} className="ml-4 border-l-2 border-blue-200 pl-4">
                          <h4 className="font-semibold">楼层: {floor.floorName}</h4>

                          {floor.rooms && floor.rooms.map((room: any, roomIndex: number) => (
                            <div key={roomIndex} className="ml-4 mt-2">
                              <h5 className="font-medium">房间: {room.roomName}</h5>

                              {room.vents && room.vents.map((vent: any, ventIndex: number) => (
                                <div key={ventIndex} className="ml-4 mt-1 p-2 bg-gray-50 rounded text-sm">
                                  <p><strong>类型:</strong> {vent.systemType} ({vent.originalType})</p>
                                  <p><strong>尺寸:</strong> {vent.dimensions?.length}×{vent.dimensions?.width}mm</p>
                                  <p><strong>数量:</strong> {vent.quantity}</p>
                                  <p><strong>颜色:</strong> {vent.color}</p>
                                  <p><strong>单价:</strong> {vent.unitPrice || '未设置'} 元</p>
                                  {vent.notes && <p><strong>备注:</strong> {vent.notes}</p>}
                                </div>
                              ))}
                            </div>
                          ))}
                        </div>
                      ))}
                    </div>
                  ))}
                  
                  <details className="mt-4">
                    <summary className="cursor-pointer font-medium">查看原始JSON结果</summary>
                    <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto">
                      {JSON.stringify(result, null, 2)}
                    </pre>
                  </details>
                </div>
              ) : (
                <div className="text-gray-500 text-center py-8">
                  点击"开始AI识别"查看结果
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 测试用例 */}
        <Card>
          <CardHeader>
            <CardTitle>预设测试用例</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button
                variant="outline"
                onClick={() => setInputText('回风200x1600，出风口2640X150，回风口2640X300.回风口2000X300，出风口2000X145，回风口4840X300.出风口2470X150，回风口2470X300，回风口1600X300，1600x145，出风口2000X150，回风口1600X300')}
              >
                测试用例1: 多尺寸列表
              </Button>
              
              <Button
                variant="outline"
                onClick={() => setInputText('容桂壹号7栋2单元903\n出风口3300×150mm\n回风口1700×300mm')}
              >
                测试用例2: 项目地址+风口
              </Button>
              
              <Button
                variant="outline"
                onClick={() => setInputText('出风1650×150×22个普通下出\n回风1650×300×22个\n出风1300✖️220✖️14个普通下出')}
              >
                测试用例3: 带数量格式
              </Button>
              
              <Button
                variant="outline"
                onClick={() => setInputText('金桂苑\n一楼\n活动室\n出风口1200×300×2个\n回风口1500×300×1个')}
              >
                测试用例4: 完整项目结构
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
