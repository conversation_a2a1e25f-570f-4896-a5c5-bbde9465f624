import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { UserR<PERSON>, Admin, FactoryUser, Client, User } from '@/types'
import { isJWTExpired } from '@/lib/auth/jwt'

// 认证角色类型 - 用于区分不同的用户类型
export type AuthRole = 'admin' | 'factory' | 'client'

interface AuthState {
  isAuthenticated: boolean
  user: Admin | FactoryUser | Client | null
  role: AuthRole | null
  factoryId: string | null
  accessToken: string | null
  refreshToken: string | null
  tokenType: string | null
  expiresIn: string | null
}

interface AuthActions {
  login: (
    user: Admin | FactoryUser | Client,
    role: AuthRole,
    tokens: {
      accessToken: string
      refreshToken: string
      tokenType: string
      expiresIn: string
    },
    factoryId?: string
  ) => void
  logout: () => Promise<void>
  updateUser: (user: Partial<Admin | FactoryUser | Client>) => void
  updateTokens: (tokens: {
    accessToken: string
    refreshToken: string
    tokenType: string
    expiresIn: string
  }) => void
  isTokenExpired: () => boolean
  hydrate: () => void
}

type AuthStore = AuthState & AuthActions

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  role: null,
  factoryId: null,
  accessToken: null,
  refreshToken: null,
  tokenType: null,
  expiresIn: null,
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      login: (user, role, tokens, factoryId) => {
        const newState = {
          isAuthenticated: true,
          user,
          role,
          factoryId: factoryId || null,
          accessToken: tokens.accessToken,
          refreshToken: tokens.refreshToken,
          tokenType: tokens.tokenType,
          expiresIn: tokens.expiresIn,
        }

        set(newState)

        // 同时保存到 localStorage 以确保兼容性
        if (typeof window !== 'undefined') {
          localStorage.setItem('accessToken', tokens.accessToken)
          localStorage.setItem('refreshToken', tokens.refreshToken)
          localStorage.setItem('user', JSON.stringify(user))
          localStorage.setItem('role', role)
          if (factoryId) {
            localStorage.setItem('factoryId', factoryId)
          }
        }

        console.log('✅ 用户登录状态已保存:', user.name)
      },

      logout: async () => {
        const currentState = get()

        // 🔧 新增：通知服务器会话结束
        if (currentState.user && currentState.accessToken) {
          try {
            console.log('📝 通知服务器会话结束')

            // 调用登出API
            const response = await fetch('/api/auth/logout', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${currentState.accessToken}`
              }
            })

            if (response.ok) {
              console.log('✅ 服务器会话已清理')
            } else {
              console.warn('⚠️ 服务器会话清理可能未完成')
            }
          } catch (error) {
            console.error('❌ 通知服务器登出失败:', error)
            // 不影响登出流程
          }
        }

        set(initialState)

        // 清除 localStorage
        if (typeof window !== 'undefined') {
          localStorage.removeItem('accessToken')
          localStorage.removeItem('refreshToken')
          localStorage.removeItem('user')
          localStorage.removeItem('role')
          localStorage.removeItem('factoryId')
          localStorage.removeItem('auth-storage')
        }

        // 停止 Token 监控（异步导入以避免循环依赖）
        if (typeof window !== 'undefined') {
          import('@/lib/services/token-monitor.service').then(({ tokenMonitorService }) => {
            tokenMonitorService.onUserLogout()
          }).catch(() => {
            // 忽略导入错误
          })
        }

        // 🔧 暂时禁用会话监控停止，避免chunk加载问题
        console.log('ℹ️ 会话监控停止已禁用，避免chunk加载问题')

        console.log('✅ 用户登录状态已清除')
      },

      updateUser: (userData) => {
        const currentUser = get().user
        if (currentUser) {
          const updatedUser = { ...currentUser, ...userData }
          set({
            user: updatedUser as Client | FactoryUser | Admin | null
          })

          // 同步到 localStorage
          if (typeof window !== 'undefined') {
            localStorage.setItem('user', JSON.stringify(updatedUser))
          }
        }
      },

      updateTokens: (tokens) => {
        set({
          accessToken: tokens.accessToken,
          refreshToken: tokens.refreshToken,
          tokenType: tokens.tokenType,
          expiresIn: tokens.expiresIn,
        })

        // 同步到 localStorage
        if (typeof window !== 'undefined') {
          localStorage.setItem('accessToken', tokens.accessToken)
          localStorage.setItem('refreshToken', tokens.refreshToken)
        }
      },

      isTokenExpired: () => {
        const { accessToken } = get()
        if (!accessToken) return true

        return isJWTExpired(accessToken)
      },

      hydrate: () => {
        // 从 localStorage 恢复状态
        if (typeof window !== 'undefined') {
          try {
            const accessToken = localStorage.getItem('accessToken')
            const refreshToken = localStorage.getItem('refreshToken')
            const userStr = localStorage.getItem('user')
            const role = localStorage.getItem('role') as AuthRole
            const factoryId = localStorage.getItem('factoryId')

            console.log('🔄 开始恢复认证状态:', {
              hasAccessToken: !!accessToken,
              hasUser: !!userStr,
              role,
              factoryId: factoryId || 'null',
              timestamp: new Date().toISOString()
            })

            if (accessToken && userStr && role) {
              // 验证token是否过期
              const isExpired = isJWTExpired(accessToken)
              if (isExpired) {
                console.log('❌ Token已过期，清理本地存储')
                localStorage.removeItem('accessToken')
                localStorage.removeItem('refreshToken')
                localStorage.removeItem('user')
                localStorage.removeItem('role')
                localStorage.removeItem('factoryId')
                localStorage.removeItem('auth-storage')
                return
              }

              const user = JSON.parse(userStr)

              // 立即设置状态
              set({
                isAuthenticated: true,
                user,
                role,
                factoryId,
                accessToken,
                refreshToken,
                tokenType: 'Bearer',
                expiresIn: '7d'
              })

              console.log('✅ 认证状态已从本地存储恢复:', {
                userName: user.name,
                role,
                factoryId: factoryId || 'null',
                timestamp: new Date().toISOString()
              })

              // 特别处理工厂用户的factoryId恢复
              if (role === 'factory' && factoryId) {
                console.log('🏭 工厂用户factoryId恢复确认:', {
                  factoryId,
                  userRole: role,
                  timestamp: new Date().toISOString()
                })

                // 延迟再次确认状态已正确设置
                setTimeout(() => {
                  const currentState = get()
                  console.log('🔍 延迟确认工厂用户状态:', {
                    isAuthenticated: currentState.isAuthenticated,
                    role: currentState.role,
                    factoryId: currentState.factoryId,
                    timestamp: new Date().toISOString()
                  })
                }, 500)
              }

              // 🔧 简化：移除定期会话监控
              console.log('✅ 认证状态恢复完成，会话验证将在API请求时进行')
            } else {
              console.log('⚠️ 本地存储中缺少必要的认证信息，跳过状态恢复')
              // 清理不完整的数据
              localStorage.removeItem('accessToken')
              localStorage.removeItem('refreshToken')
              localStorage.removeItem('user')
              localStorage.removeItem('role')
              localStorage.removeItem('factoryId')
              localStorage.removeItem('auth-storage')
            }
          } catch (error) {
            console.error('❌ 恢复认证状态失败:', error)
            // 清除可能损坏的数据
            localStorage.removeItem('accessToken')
            localStorage.removeItem('refreshToken')
            localStorage.removeItem('user')
            localStorage.removeItem('role')
            localStorage.removeItem('factoryId')
            localStorage.removeItem('auth-storage')

            // 重置状态
            set(initialState)
          }
        }
      },
    }),
    {
      name: 'auth-storage',
      // 只持久化关键数据
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        role: state.role,
        factoryId: state.factoryId,
        accessToken: state.accessToken,
        refreshToken: state.refreshToken,
        tokenType: state.tokenType,
        expiresIn: state.expiresIn,
      }),
    }
  )
)

// 辅助函数
export const useIsAdmin = () => {
  const user = useAuthStore(state => state.user)
  const userType = (useAuthStore.getState().user as User)?.userType
  return userType === 'admin' || (user && 'username' in user && 'email' in user && !('factoryId' in user))
}

export const useIsFactory = () => {
  const user = useAuthStore(state => state.user)
  const userType = (useAuthStore.getState().user as User)?.userType
  return userType === 'factory_user' || (user && 'factoryId' in user)
}

export const useIsClient = () => {
  const user = useAuthStore(state => state.user)
  const userType = (useAuthStore.getState().user as User)?.userType
  return userType === 'client' || (user && !('username' in user) && !('factoryId' in user))
}

export const useCurrentFactoryId = () => {
  return useAuthStore(state => state.factoryId)
}
